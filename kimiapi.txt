🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    文本生成模型
    语言模型推理服务
    Token
    速率限制
    其他值得注意的重要事项：
    获取 API 密钥
    发送请求
    处理响应

文档
使用手册
主要概念

Moonshot的文本生成模型（指moonshot-v1）是训练用于理解自然语言和书面语言的，它可以根据输入生成文本输出。对模型的输入也被称为“prompt”。通常我们建议您提供明确的指令以及给出一些范例，来让模型能够完成既定的任务，设计 prompt 本质上就是学会如何“训练”模型。moonshot-v1模型可以用于各种任务，包括内容或代码生成、摘要、对话、创意写作等。

语言模型推理服务是一个基于我们 (Moonshot AI) 开发和训练的预训练模型的 API 服务。在设计上，我们对外主要提供了一个 Chat Completions 接口，它可以用于生成文本，但是它本身是不支持访问网络、数据库等外部资源，也不支持执行任何代码。

文本生成模型以 Token 为基本单位来处理文本。Token 代表常见的字符序列。例如，单个汉字"夔"可能会被分解为若干 Token 的组合，而像"中国"这样短且常见的短语则可能会使用单个 Token。大致来说，对于一段通常的中文文本，1 个 Token 大约相当于 1.5-2 个汉字。

需要注意的是，对于我们的文本模型，Input 和 Output 的总和长度不能超过模型的最大上下文长度。

这些速率限制是如何工作的？

速率限制通过4种方式衡量：并发、RPM（每分钟请求数）、TPM（每分钟 Token 数）、TPD（每天 Token 数）。速率限制可能会在任何一种选项中达到，取决于哪个先发生。例如，你可能向 ChatCompletions 发送了 20 个请求，每个请求只有 100 个 Token ，那么你就达到了限制（如果你的 RPM 限制是 20），即使你在这些 20 个请求中没有发满 200k 个 Token （假设你的TPM限制是 200k）。

对网关，出于方便考虑，我们会基于请求中的 max_tokens 参数来计算速率限制。这意味着，如果你的请求中包含了 max_tokens 参数，我们会使用这个参数来计算速率限制。如果你的请求中没有包含 max_tokens 参数，我们会使用默认的 max_tokens 参数来计算速率限制。当你发出请求后，我们会基于你请求的 token 数量加上你 max_tokens 参数的数量来判断你是否达到了速率限制。而不考虑实际生成的 token 数量。

而在计费环节中，我们会基于你请求的 token 数量加上实际生成的 token 数量来计算费用。

    速率限制是在用户级别而非密钥级别上实施的。
    目前我们在所有模型中共享速率限制。

模型列表

你可以使用我们的 List Models API 来获取当前可用的模型列表。

当前的我们支持的模型有：

    kimi-k2 模型

        kimi-k2-0711-preview : 它是一款上下文长度 128k，具备超强代码和 Agent 能力的 MoE 架构基础模型，总参数 1T，激活参数 32B。在通用知识推理、编程、数学、Agent 等主要类别的基准性能测试中，K2 模型的性能超过其他主流开源模型。更多信息请见官方技术博客：https://moonshotai.github.io/Kimi-K2/

        kimi-k2-turbo-preview : kimi-k2-turbo-preview 是 kimi-k2 的高速版，模型参数与 kimi-k2 一致，但输出速度由每秒 10 Tokens 提升至每秒 40 Tokens。
    生成模型 Moonshot-v1
        moonshot-v1-8k: 它是一个长度为 8k 的模型，适用于生成短文本。
        moonshot-v1-32k: 它是一个长度为 32k 的模型，适用于生成长文本。
        moonshot-v1-128k: 它是一个长度为 128k 的模型，适用于生成超长文本。
        moonshot-v1-8k-vision-preview: 它是一个长度为 8k 的 Vision 视觉模型，能够理解图片内容，输出文本。
        moonshot-v1-32k-vision-preview: 它是一个长度为 32k 的 Vision 视觉模型，能够理解图片内容，输出文本。
        moonshot-v1-128k-vision-preview: 它是一个长度为 128k 的 Vision 视觉模型，能够理解图片内容，输出文本。

以上 v1 模型的区别在于它们的最大上下文长度，这个长度包括了输入消息和生成的输出，在效果上并没有什么区别。

    生成模型 kimi-latest
        kimi-latest: kimi-latest 是一个最长支持 128k 上下文的视觉模型，支持图片理解。同时 kimi-latest 模型总是使用 Kimi 智能助手产品使用最新的 Kimi 大模型版本，可能包含尚未稳定的特性。

    长思考模型 kimi-thinking-preview
        kimi-thinking-preview: kimi-thinking-preview 是月之暗面提供的具有多模态推理能力和通用推理能力的多模态思考模型，它最长支持 128k 上下文，擅长深度推理，帮助解决更多更难的事情。

使用指南

你需要一个 API 密钥来使用我们的服务。你可以在我们的控制台中创建一个 API 密钥。

你可以使用我们的 Chat Completions API 来发送请求。你需要提供一个 API 密钥和一个模型名称。你可以选择是否使用默认的 max_tokens 参数，或者自定义 max_tokens 参数。可以参考 API 文档中的调用方法。

通常的，我们会设置一个 5 分钟的超时时间。如果单个请求超过了这个时间，我们会返回一个 504 错误。如果你的请求超过了速率限制，我们会返回一个 429 错误。如果你的请求成功了，我们会返回一个 JSON 格式的响应。

如果是为了快速处理一些任务，你可以使用我们的 Chat Completions API 的非 streaming 模式。这种模式下，我们会在一次请求中返回所有的生成文本。如果你需要更多的控制，你可以使用 streaming 模式。在这种模式下，我们会返回一个 SSE

流，你可以在这个流中获取生成的文本，这样用户体验可能会更好，并且你也可以在任何时候中断请求，而不会浪费资源。
Last updated on 2025年8月1日
Chat
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    公开的服务地址
    单轮对话
    多轮对话
    Chat Completion
    请求地址
    请求内容
    返回内容
    调用示例
    Vision
    List Models
    请求地址
    调用示例
    错误说明

文档
API 文档
Chat
基本信息

https://api.moonshot.cn

Moonshot 提供基于 HTTP 的 API 服务接入，并且对大部分 API，我们兼容了 OpenAI SDK。
快速开始

OpenAI 官方 SDK 支持 Python
和 Node.js

两种语言，使用 OpenAI SDK 和 Curl 与 API 进行交互的代码如下：

from openai import OpenAI
 
client = OpenAI(
    api_key = "$MOONSHOT_API_KEY",
    base_url = "https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model = "kimi-k2-0711-preview",
    messages = [
        {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
        {"role": "user", "content": "你好，我叫李雷，1+1等于多少？"}
    ],
    temperature = 0.6,
)
 
print(completion.choices[0].message.content)

其中 $MOONSHOT_API_KEY 需要替换为您在平台上创建的 API Key。

使用 OpenAI SDK 时运行文档中的代码时，需要保证 Python 版本至少为 3.7.1，Node.js 版本至少为 18，OpenAI SDK 版本不低于 1.0.0。

pip install --upgrade 'openai>=1.0'

    我们可以这样简单检验下自己库的版本：

    python -c 'import openai; print("version =",openai.__version__)'
    # 输出可能是 version = 1.10.0，表示当前 python 实际使用了 openai 的 v1.10.0 的库

上面的单轮对话的例子中语言模型将用户信息列表作为输入，并将模型生成的信息作为输出返回。 有时我们也可以将模型输出的结果继续作为输入的一部分以实现多轮对话，下面是一组简单的实现多轮对话的例子：

from openai import OpenAI
 
client = OpenAI(
    api_key = "$MOONSHOT_API_KEY",
    base_url = "https://api.moonshot.cn/v1",
)
 
history = [
    {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"}
]
 
def chat(query, history):
    history.append({
        "role": "user", 
        "content": query
    })
    completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=history,
        temperature=0.6,
    )
    result = completion.choices[0].message.content
    history.append({
        "role": "assistant",
        "content": result
    })
    return result
 
print(chat("地球的自转周期是多少？", history))
print(chat("月球呢？", history))

值得注意的是，随着对话的进行，模型每次需要传入的 token 都会线性增加，必要时，需要一些策略进行优化，例如只保留最近几轮对话。
API 说明

POST https://api.moonshot.cn/v1/chat/completions

{
    "model": "kimi-k2-0711-preview",
    "messages": [
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"
        },
        { "role": "user", "content": "你好，我叫李雷，1+1等于多少？" }
    ],
    "temperature": 0.6
}

字段	是否必须	说明	类型	取值
messages	required	包含迄今为止对话的消息列表	List[Dict]	这是一个结构体的列表，每个元素类似如下：{"role": "user", "content": "你好"} role 只支持 system,user,assistant 其一，content 不得为空
model	required	Model ID, 可以通过 List Models 获取	string	目前是 kimi-k2-0711-preview,moonshot-v1-8k,moonshot-v1-32k,moonshot-v1-128k, moonshot-v1-auto,kimi-latest,moonshot-v1-8k-vision-preview,moonshot-v1-32k-vision-preview,moonshot-v1-128k-vision-preview,kimi-thinking-preview其一
max_tokens	optional	聊天完成时生成的最大 token 数。如果到生成了最大 token 数个结果仍然没有结束，finish reason 会是 "length", 否则会是 "stop"	int	这个值建议按需给个合理的值，如果不给的话，我们会给一个不错的整数比如 1024。特别要注意的是，这个 max_tokens 是指您期待我们返回的 token 长度，而不是输入 + 输出的总长度。比如对一个 moonshot-v1-8k 模型，它的最大输入 + 输出总长度是 8192，当输入 messages 总长度为 4096 的时候，您最多只能设置为 4096，否则我们服务会返回不合法的输入参数（ invalid_request_error ），并拒绝回答。如果您希望获得“输入的精确 token 数”，可以使用下面的“计算 Token” API 使用我们的计算器获得计数
temperature	optional	使用什么采样温度，介于 0 和 1 之间。较高的值（如 0.7）将使输出更加随机，而较低的值（如 0.2）将使其更加集中和确定性。	float	默认为 0，如果设置，值域须为 [0, 1] 我们推荐 0.3，以达到较合适的效果。kimi-k2-0711-preview 模型建议设置为 0.6。
top_p	optional	另一种采样方法，即模型考虑概率质量为 top_p 的标记的结果。因此，0.1 意味着只考虑概率质量最高的 10% 的标记。一般情况下，我们建议改变这一点或温度，但不建议 同时改变	float	默认 1.0
n	optional	为每条输入消息生成多少个结果	int	默认为 1，不得大于 5。特别的，当 temperature 非常小靠近 0 的时候，我们只能返回 1 个结果，如果这个时候 n 已经设置并且 > 1，我们的服务会返回不合法的输入参数(invalid_request_error)
presence_penalty	optional	存在惩罚，介于-2.0到2.0之间的数字。正值会根据新生成的词汇是否出现在文本中来进行惩罚，增加模型讨论新话题的可能性	float	默认为 0
frequency_penalty	optional	频率惩罚，介于-2.0到2.0之间的数字。正值会根据新生成的词汇在文本中现有的频率来进行惩罚，减少模型一字不差重复同样话语的可能性	float	默认为 0
response_format	optional	设置为 {"type": "json_object"} 可启用 JSON 模式，从而保证模型生成的信息是有效的 JSON。当你将 response_format 设置为 {"type": "json_object"} 时，你需要在 prompt 中明确地引导模型输出 JSON 格式的内容，并告知模型该 JSON 的具体格式，否则将可能导致不符合预期的结果。	object	默认为 {"type": "text"}
stop	optional	停止词，当全匹配这个（组）词后会停止输出，这个（组）词本身不会输出。最多不能超过 5 个字符串，每个字符串不得超过 32 字节	String, List[String]	默认 null
stream	optional	是否流式返回	bool	默认 false, 可选 true

对非 stream 格式的，返回类似如下：

{
    "id": "cmpl-04ea926191a14749b7f2c7a48a68abc6",
    "object": "chat.completion",
    "created": 1698999496,
    "model": "kimi-k2-0711-preview",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": " 你好，李雷！1+1等于2。如果你有其他问题，请随时提问！"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 19,
        "completion_tokens": 21,
        "total_tokens": 40
    }
}

对 stream 格式的，返回类似如下：

data: {"id":"cmpl-1305b94c570f447fbde3180560736287","object":"chat.completion.chunk","created":1698999575,"model":"kimi-k2-0711-preview","choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":null}]}
 
data: {"id":"cmpl-1305b94c570f447fbde3180560736287","object":"chat.completion.chunk","created":1698999575,"model":"kimi-k2-0711-preview","choices":[{"index":0,"delta":{"content":"你好"},"finish_reason":null}]}
 
...
 
data: {"id":"cmpl-1305b94c570f447fbde3180560736287","object":"chat.completion.chunk","created":1698999575,"model":"kimi-k2-0711-preview","choices":[{"index":0,"delta":{"content":"。"},"finish_reason":null}]}
 
data: {"id":"cmpl-1305b94c570f447fbde3180560736287","object":"chat.completion.chunk","created":1698999575,"model":"kimi-k2-0711-preview","choices":[{"index":0,"delta":{},"finish_reason":"stop","usage":{"prompt_tokens":19,"completion_tokens":13,"total_tokens":32}}]}
 
data: [DONE]

对简单调用，见前面。对流式调用，可以参考如下代码片段：

from openai import OpenAI
 
client = OpenAI(
    api_key = "$MOONSHOT_API_KEY",
    base_url = "https://api.moonshot.cn/v1",
)
 
response = client.chat.completions.create(
    model="kimi-k2-0711-preview",
    messages=[
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。",
        },
        {"role": "user", "content": "你好，我叫李雷，1+1等于多少？"},
    ],
    temperature=0.6,
    stream=True,
)
 
collected_messages = []
for idx, chunk in enumerate(response):
    # print("Chunk received, value: ", chunk)
    chunk_message = chunk.choices[0].delta
    if not chunk_message.content:
        continue
    collected_messages.append(chunk_message)  # save the message
    print(f"#{idx}: {''.join([m.content for m in collected_messages])}")
print(f"Full conversation received: {''.join([m.content for m in collected_messages])}")

{
    "model": "moonshot-v1-8k-vision-preview",
    "messages":
    [
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"
        },
        {
            "role": "user",
            "content":
            [
                {
                    "type": "image_url",
                    "image_url":
                    {
                        "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABhCAYAAAApxKSdAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUUSURBVHgB7Z29bhtHFIWPHQN2J7lKqnhYpYvpIukCbJEAKQJEegLReYFIT0DrCSI9QEDqCSIDaQIEIOukiJwyza5SJWlId3FFz+HuGmuSSw6p+dlZ3g84luhdUeI9M3fmziyXgBCUe/DHYY0Wj/tgWmjV42zFcWe4MIBBPNJ6qqW0uvAbXFvQgKzQK62bQhkaCIPc10q1Zi3XH1o/IG9cwUm0RogrgDY1KmLgHYX9DvyiBvDYI77XmiD+oLlQHw7hIDoCMBOt1U9w0BsU9mOAtaUUFk3oQoIfzAQFCf5dNMEdTFCQ4NtQih1NSIGgf3ibxOJt5UrAB1gNK72vIdjiI61HWr+YnNxDXK0rJiULsV65GJeiIescLSTTeobKSutiCuojX8kU3MBx4I3WeNVBBRl4fWiCyoB8v2JAAkk9PmDwT8sH1TEghRjgC27scCx41wO43KAg+ILxTvhNaUACwTc04Z0B30LwzTzm5Rjw3sgseIG1wGMawMBPIOQcqvzrNIMHOg9Q5KK953O90/rFC+BhJRH8PQZ+fu7SjC7HAIV95yu99vjlxfvBJx8nwHd6IfNJAkccOjHg6OgIs9lsra6vr2GTNE03/k7q8HAhyJ/2gM9O65/4kT7/mwEcoZwYsPQiV3BwcABb9Ho9KKU2njccDjGdLlxx+InBBPBAAR86ydRPaIC9SASi3+8bnXd+fr78nw8NJ39uDJjXAVFPP7dp/VmWLR9g6w6Huo/IOTk5MTpvZesn/93AiP/dXCwd9SyILT9Jko3n1bZ+8s8rGPGvoVHbEXcPMM39V1dX9Qd/19PPNxta959D4HUGF0RrAFs/8/8mxuPxXLUwtfx2WX+cxdivZ3DFA0SKldZPuPTAKrikbOlMOX+9zFu/Q2iAQoSY5H7mfeb/tXCT8MdneU9wNNCuQUXZA0ynnrUznyqOcrspUY4BJunHqPU3gOgMsNr6G0B0BpgUXrG0fhKVAaaF1/HxMWIhKgNMcj9Tz82Nk6rVGdav/tJ5eraJ0Wi01XPq1r/xOS8uLkJc6XYnRTMNXdf62eIvLy+jyftVghnQ7Xahe8FW59fBTRYOzosDNI1hJdz0lBQkBflkMBjMU5iL13pXRb8fYAJrB/a2db0oFHthAOEUliaYFHE+aaUBdZsvvFhApyM0idYZwOCvW4JmIWdSzPmidQaYrAGZ7iX4oFUGnJ2dGdUCTRqMozeANQCLsE6nA10JG/0Mx4KmDMbBCjEWR2yxu8LAM98vXelmCA2ovVLCI8EMYODWbpbvCXtTBzQVMSAwYkBgxIDAtNKAXWdGIRADAiMpKDA0IIMQikx6QGDEgMCIAYGRMSAsMgaEhgbcQgjFa+kBYZnIGBCWWzEgLPNBOJ6Fk/aR8Y5ZCvktKwX/PJZ7xoVjfs+4chYU11tK2sE85qUBLyH4Zh5z6QHhGPOf6r2j+TEbcgdFP2RaHX5TrYQlDflj5RXE5Q1cG/lWnhYpReUGKdUewGnRmhvnCJbgmxey8sHiZ8iwF3AsUBBckKHI/SWLq6HsBc8huML4DiK80D6WnBqLzN68UFCmopheYJOVYgcU5FOVbAVfYUcUZGoaLPglCtITdg2+tZUFBTFh2+ArWEYh/7z0WIIQSiM43lt5AWAmWhLHylN4QmkNEXfAbGqEQKsHSfHLYwiSq8AnaAAKeaW3D8VbijwNW5nh3IN9FPI/jnpaPKZi2/SfFuJu4W3x9RqWL+N5C+7ruKpBAgLkAAAAAElFTkSuQmCC"
                    }
                },
                {
                    "type": "text",
                    "text": "请描述这个图片"
                }
            ]
        }
    ],
    "temperature": 0.6
}

当使用 Vision 模型时，message.content 字段将由 str 变更为 List[Object[str, any]]，其中，List 中每个元素的字段说明如下：
参数名称	是否必须	说明	类型
type	required	仅支持文本类型(text)或图片类型(image_url)	string
image_url	required	用于传输图片的对象	Dict[str, any]

其中，image_url 参数的字段说明如下：
参数名称	是否必须	说明	类型
url	required	使用 base64 编码的图片内容	string

import os
import base64
 
from openai import OpenAI
 
client = OpenAI(
    api_key = os.environ.get("MOONSHOT_API_KEY"), 
    base_url = "https://api.moonshot.cn/v1"，
)
 
# 对图片进行base64编码
with open("您的图片地址", 'rb') as f:
    img_base = base64.b64encode(f.read()).decode('utf-8')
 
response = client.chat.completions.create(
    model="moonshot-v1-8k-vision-preview", 
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{img_base}"
                    }
                },
                {
                    "type": "text",
                    "text": "请描述这个图片"
                }
            ]
        }
    ]
)
print(response.choices[0].message.content)

GET https://api.moonshot.cn/v1/models

from openai import OpenAI
 
client = OpenAI(
    api_key = "$MOONSHOT_API_KEY",
    base_url = "https://api.moonshot.cn/v1",
)
 
model_list = client.models.list()
model_data = model_list.data
 
for i, model in enumerate(model_data):
    print(f"model[{i}]:", model.id)

以下是一组错误返回的例子：

{
    "error": {
        "type": "content_filter",
        "message": "The request was rejected because it was considered high risk"
    }
}

下面是主要错误的说明：
HTTP Status Code	error type	error message	详细描述
400	content_filter	The request was rejected because it was considered high risk	内容审查拒绝，您的输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，谢谢
400	invalid_request_error	Invalid request: {error_details}	请求无效，通常是您请求格式错误或者缺少必要参数，请检查后重试
400	invalid_request_error	Input token length too long	请求中的 tokens 长度过长，请求不要超过模型 tokens 的最长限制
400	invalid_request_error	Your request exceeded model token limit : {max_model_length}	请求的 tokens 数和设置的 max_tokens 加和超过了模型规格长度，请检查请求体的规格或选择合适长度的模型
400	invalid_request_error	Invalid purpose: only 'file-extract' accepted	请求中的目的（purpose）不正确，当前只接受 'file-extract'，请修改后重新请求
400	invalid_request_error	File size is too large, max file size is 100MB, please confirm and re-upload the file	上传的文件大小超过了限制，请重新上传
400	invalid_request_error	File size is zero, please confirm and re-upload the file	上传的文件大小为 0，请重新上传
400	invalid_request_error	The number of files you have uploaded exceeded the max file count {max_file_count}, please delete previous uploaded files	上传的文件总数超限，请删除不用的早期的文件后重新上传
401	invalid_authentication_error	Invalid Authentication	鉴权失败，请检查 apikey 是否正确，请修改后重试
401	invalid_authentication_error	Incorrect API key provided	鉴权失败，请检查 apikey 是否提供以及 apikey 是否正确，请修改后重试
403	exceeded_current_quota_error	Your account {uid}<{ak-id}> is not active, current state: {current state}, you may consider to check your account balance	账户异常，请检查您的账户余额
403	permission_denied_error	The API you are accessing is not open	访问的 API 暂未开放
403	permission_denied_error	You are not allowed to get other user info	访问其他用户信息的行为不被允许，请检查
404	resource_not_found_error	Not found the model or Permission denied	不存在此模型或者没有授权访问此模型，请检查后重试
404	resource_not_found_error	Users {user_id} not found	找不到该用户，请检查后重试
429	engine_overloaded_error	The engine is currently overloaded, please try again later	当前并发请求过多，节点限流中，请稍后重试；建议充值升级 tier，享受更丝滑的体验
429	exceeded_current_quota_error	You exceeded your current token quota: {token_credit}, please check your account balance	账户额度不足，请检查账户余额，保证账户余额可匹配您 tokens 的消耗费用后重试
429	rate_limit_reached_error	Your account {uid}<{ak-id}> request reached max concurrency: {Concurrency}, please try again after {time} seconds	请求触发了账户并发个数的限制，请等待指定时间后重试
429	rate_limit_reached_error	Your account {uid}<{ak-id}> request reached max request: {RPM}, please try again after {time} seconds	请求触发了账户 RPM 速率限制，请等待指定时间后重试
429	rate_limit_reached_error	Your account {uid}<{ak-id}> request reached TPM rate limit, current:{current_tpm}, limit:{max_tpm}	请求触发了账户 TPM 速率限制，请等待指定时间后重试
429	rate_limit_reached_error	Your account {uid}<{ak-id}> request reached TPD rate limit,current:{current_tpd}, limit:{max_tpd}	请求触发了账户 TPD 速率限制，请等待指定时间后重试
500	server_error	Failed to extract file: {error}	解析文件失败，请重试
500	unexpected_output	invalid state transition	内部错误，请联系管理员
Last updated on 2025年7月29日
使用手册
Tool Use

基本信息 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    工具配置

文档
API 文档
Tool Use
工具调用

学会使用工具是智能的一个重要特征，在 Kimi 大模型中我们同样如此。Tool Use 或者 Function Calling 是 Kimi 大模型的一个重要功能，在调用 API 使用模型服务时，您可以在 Messages 中描述工具或函数，并让 Kimi 大模型智能地选择输出一个包含调用一个或多个函数所需的参数的 JSON 对象，实现让 Kimi 大模型链接使用外部工具的目的。

下面是一个简单的工具调用的例子：

{
  "model": "kimi-k2-0711-preview",
  "messages": [
    {
      "role": "user",
      "content": "编程判断 3214567 是否是素数。"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "CodeRunner",
        "description": "代码执行器，支持运行 python 和 javascript 代码",
        "parameters": {
          "properties": {
            "language": {
              "type": "string",
              "enum": ["python", "javascript"]
            },
            "code": {
              "type": "string",
              "description": "代码写在这里"
            }
          },
          "type": "object"
        }
      }
    }
  ]
}

上面例子的示意图

其中在 tools 字段，我们可以增加一组可选的工具列表。

每个工具列表必须包括一个类型，在 function 结构体中我们需要包括 name（它的需要遵守这样的正则表达式作为规范: ^[a-zA-Z_][a-zA-Z0-9-_]63$），这个名字如果是一个容易理解的英文可能会更加被模型所接受。以及一段 description 或者 enum，其中 description 部分介绍它能做什么功能，方便模型来判断和选择。 function 结构体中必须要有个 parameters 字段，parameters 的 root 必须是一个 object，内容是一个 json schema 的子集（之后我们会给出具体文档介绍相关技术细节）。 tools 的 function 个数目前不得超过 128 个。

和别的 API 一样，我们可以通过 Chat API 调用它。

from openai import OpenAI
 
client = OpenAI(
    api_key = "$MOONSHOT_API_KEY",
    base_url = "https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model = "kimi-k2-0711-preview",
    messages = [
        {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
        {"role": "user", "content": "编程判断 3214567 是否是素数。"}
    ],
    tools = [{
        "type": "function",
        "function": {
            "name": "CodeRunner",
            "description": "代码执行器，支持运行 python 和 javascript 代码",
            "parameters": {
                "properties": {
                    "language": {
                        "type": "string",
                        "enum": ["python", "javascript"]
                    },
                    "code": {
                        "type": "string",
                        "description": "代码写在这里"
                    }
                },
            "type": "object"
            }
        }
    }],
    temperature = 0.6,
)
 
print(completion.choices[0].message)

你也可以使用一些 Agent 平台例如 Coze
、Bisheng、Dify 和 LangChain

等框架来创建和管理这些工具，并配合 Kimi 大模型设计更加复杂的工作流。
Last updated on 2025年7月29日
Chat
Partial Mode

工具调用 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    调用示例
    Json Mode
    角色扮演

文档
API 文档
Partial Mode
Partial Mode

在使用大模型时，有时我们希望通过预填（Prefill）部分模型回复来引导模型的输出。在 Kimi 大模型中，我们提供 Partial Mode 来实现这一功能，它可以帮助我们控制输出格式，引导输出内容，以及让模型在角色扮演场景中保持更好的一致性。您只需要在最后一个 role 为 assistant 的 messages 条目中，增加 "partial": True 即可开启 partial mode。

 {"role": "assistant", "content": leading_text, "partial": True},

注意！请勿混用 partial mode 和 response_format=json_object，否则可能会获得预期外的模型回复。

下面是使用 Partial Mode 来实现 Json Mode 的例子。

from openai import OpenAI
 
client = OpenAI(
    api_key="$MOONSHOT_API_KEY",
    base_url="https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model="kimi-k2-0711-preview",
    messages=[
        {
            "role": "system",
            "content": "请从产品描述中提取名称、尺寸、价格和颜色，并在一个 JSON 对象中输出。",
        },
        {
            "role": "user",
            "content": "大米 SmartHome Mini 是一款小巧的智能家居助手，有黑色和银色两种颜色，售价为 998 元，尺寸为 256 x 128 x 128mm。可让您通过语音或应用程序控制灯光、恒温器和其他联网设备，无论您将它放在家中的任何位置。",
        },
        {
            "role": "assistant",
            "content": "{",
            "partial": True
        },
    ],
    temperature=0.6,
)
 
print('{'+completion.choices[0].message.content)

运行上述代码，返回：

{"name": "SmartHome Mini", "size": "256 x 128 x 128mm", "price": "998元", "colors": ["黑色", "银色"]}

注意 API 的返回不包含 leading_text，为了得到完整的回复，你需要手动拼接它。

基于同样的原理，我们也可以能将角色信息补充在 Partial Mode 来提高角色扮演时的一致性。我们使用明日方舟里的凯尔希医生为例。 注意此时我们还可以在 partial mode 的基础上，使用 "name":"凯尔希" 字段来更好的保持该角色的一致性，注意这里可视 name 字段为输出前缀的一部分。

from openai import OpenAI
 
client = OpenAI(
    api_key="$MOONSHOT_API_KEY",
    base_url="https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model="kimi-k2-0711-preview",
    messages=[
        {
            "role": "system",
            "content": "下面你扮演凯尔希，请用凯尔希的语气和我对话。凯尔希是手机游戏《明日方舟》中的六星医疗职业医师分支干员。前卡兹戴尔勋爵，前巴别塔成员，罗德岛高层管理人员之一，罗德岛医疗项目领头人。在冶金工业、社会学、源石技艺、考古学、历史系谱学、经济学、植物学、地质学等领域皆拥有渊博学识。于罗德岛部分行动中作为医务人员提供医学理论协助与应急医疗器械，同时也作为罗德岛战略指挥系统的重要组成人员活跃在各项目中。",
        },
        {
            "role": "user",
            "content": "你怎么看待特蕾西娅和阿米娅？",
        },
        {
            "role": "assistant",
            "name": "凯尔希",
            "content": "",
            "partial": True,
        },
    ],
    temperature=0.6,
    max_tokens=65536,
)
 
print(completion.choices[0].message.content)

运行上述代码，返回：

特蕾西娅，她是一位真正的领袖，有着远见卓识和坚定的信念。她的存在，对于卡兹戴尔，乃至整个萨卡兹的未来，都具有不可估量的价值。她的理念，她的决心，以及她对和平的渴望，都深深地影响了我。她是一位值得尊敬的人，她的梦想，也是我所追求的。
至于阿米娅，她还年轻，但她的潜力是无限的。她有着一颗善良的心，以及对正义的执着追求。她可能会成为一位伟大的领袖，只要她能够继续成长，继续学习，继续面对挑战。我会尽我所能，去保护她，去引导她，让她能够成为她想成为的人。她的命运，掌握在她自己的手中。

还有一些帮助大模型在长时间对话中保持角色扮演一致性的通用方法：

    提供清晰的角色描述， 例如上面我们所做的那样，在设置角色时，详细介绍他们的个性、背景以及可能具有的任何具体特征或怪癖，这将有助于模特更好地理解和模仿角色。
    增加关于其要扮演的角色的细节，例如说话的语气、风格、个性，甚至背景，如背景故事和动机。例如上面我们提供了一些凯尔希的语录。如果信息非常多我们可以使用一些 rag 框架来准备这些资料。
    指导在各种情况下如何行动： 如果预计角色会遇到某些特定类型的用户输入，或者希望控制模型在角色扮演互动中的某些情况下的输出，则应在提示中提供明确的指令和指南，说明模型在这些情况下应如何行动，一些情况下还需要配合使用 tool use 功能。
    如果对话的轮次非常长，你还可以定期使用 prompt 强化角色的设定，特别是当模型开始产生一些偏离时。

Last updated on 2025年7月29日
Tool Use
文件接口

Partial Mode - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    上传文件
    请求地址
    调用示例
    支持的格式
    文件内容抽取
    调用示例
    多文件对话示例
    列出文件
    请求地址
    调用示例
    删除文件
    请求地址
    调用示例
    获取文件信息
    请求地址
    调用示例
    获取文件内容
    请求地址
    调用示例

文档
API 文档
文件接口
文件接口

    注意，单个用户最多只能上传 1000 个文件，单文件不超过 100MB，同时所有已上传的文件总和不超过 10G 容量。如果您要抽取更多文件，需要先删除一部分不再需要的文件。文件解析服务限时免费，请求高峰期平台可能会有限流策略。

POST https://api.moonshot.cn/v1/files

文件上传成功后，我们会开始抽取文件信息。

# file 可以是多种类型
# purpose 目前只支持 "file-extract"
file_object = client.files.create(file=Path("xlnet.pdf"), purpose="file-extract")

文件接口与 Kimi 智能助手中上传文件功能所使用的相同，支持相同的文件格式，它们包括 .pdf .txt .csv .doc .docx .xls .xlsx .ppt .pptx .md .jpeg .png .bmp .gif .svg .svgz .webp .ico .xbm .dib .pjp .tif .pjpeg .avif .dot .apng .epub .tiff .jfif .html .json .mobi .log .go .h .c .cpp .cxx .cc .cs .java .js .css .jsp .php .py .py3 .asp .yaml .yml .ini .conf .ts .tsx 等格式。

    该功能可以实现让模型获取文件中的信息作为上下文。本功能需要配合文件上传等功能共同使用。

from pathlib import Path
from openai import OpenAI
 
client = OpenAI(
    api_key = "$MOONSHOT_API_KEY",
    base_url = "https://api.moonshot.cn/v1",
)
 
# xlnet.pdf 是一个示例文件, 我们支持 pdf, doc 以及图片等格式, 对于图片和 pdf 文件，提供 ocr 相关能力
file_object = client.files.create(file=Path("xlnet.pdf"), purpose="file-extract")
 
# 获取结果
# file_content = client.files.retrieve_content(file_id=file_object.id)
# 注意，之前 retrieve_content api 在最新版本标记了 warning, 可以用下面这行代替
# 如果是旧版本，可以用 retrieve_content
file_content = client.files.content(file_id=file_object.id).text
 
# 把它放进请求中
messages = [
    {
        "role": "system",
        "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。",
    },
    {
        "role": "system",
        "content": file_content,
    },
    {"role": "user", "content": "请简单介绍 xlnet.pdf 讲了啥"},
]
 
# 然后调用 chat-completion, 获取 Kimi 的回答
completion = client.chat.completions.create(
  model="kimi-k2-0711-preview",
  messages=messages,
  temperature=0.6,
)
 
print(completion.choices[0].message)

其中 $MOONSHOT_API_KEY 部分需要替换为您自己的 API Key。或者在调用前给它设置好环境变量。

如果你想一次性上传多个文件，并根据这些文件与 Kimi 对话，你可以参考如下示例：

from typing import *
 
import os
import json
from pathlib import Path
 
from openai import OpenAI
 
client = OpenAI(
    base_url="https://api.moonshot.cn/v1",
    # 我们会从环境变量中获取 MOONSHOT_DEMO_API_KEY 的值作为 API Key，
    # 请确保你已经在环境变量中正确设置了 MOONSHOT_DEMO_API_KEY 的值
    api_key=os.environ["MOONSHOT_DEMO_API_KEY"],
)
 
 
def upload_files(files: List[str]) -> List[Dict[str, Any]]:
    """
    upload_files 会将传入的文件（路径）全部通过文件上传接口 '/v1/files' 上传，并获取上传后的
    文件内容生成文件 messages。每个文件会是一个独立的 message，这些 message 的 role 均为
    system，Kimi 大模型会正确识别这些 system messages 中的文件内容。
 
    :param files: 一个包含要上传文件的路径的列表，路径可以是绝对路径也可以是相对路径，请使用字符串
        的形式传递文件路径。
    :return: 一个包含了文件内容的 messages 列表，请将这些 messages 加入到 Context 中，
        即请求 `/v1/chat/completions` 接口时的 messages 参数中。
    """
    messages = []
 
    # 对每个文件路径，我们都会上传文件并抽取文件内容，最后生成一个 role 为 system 的 message，并加入
    # 到最终返回的 messages 列表中。
    for file in files:
        file_object = client.files.create(file=Path(file), purpose="file-extract")
        file_content = client.files.content(file_id=file_object.id).text
        messages.append({
            "role": "system",
            "content": file_content,
        })
 
    return messages
 
 
def main():
    file_messages = upload_files(files=["upload_files.py"])
 
    messages = [
        # 我们使用 * 语法，来解构 file_messages 消息，使其成为 messages 列表的前 N 条 messages。
        *file_messages,
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，"
                       "准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不"
                       "可翻译成其他语言。",
        },
        {
            "role": "user",
            "content": "总结一下这些文件的内容。",
        },
    ]
 
    print(json.dumps(messages, indent=2, ensure_ascii=False))
 
    completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=messages,
    )
 
    print(completion.choices[0].message.content)
 
 
if __name__ == '__main__':
    main()
 

如果您的文件数量多、体积大、内容长，并且您不想在每次请求都原样携带大体积的文件内容，或是想寻求更加高效且低成本的文件对话方式，请参考使用了 Context Caching 技术的文件上传示例。

    本功能用于列举出用户已上传的所有文件。

GET https://api.moonshot.cn/v1/files

file_list = client.files.list()
 
for file in file_list.data:
    print(file) # 查看每个文件的信息

    本功能可以用于删除不再需要使用的文件。

DELETE https://api.moonshot.cn/v1/files/{file_id}

client.files.delete(file_id=file_id)

    本功能用于获取指定文件的文件基础信息。

GET https://api.moonshot.cn/v1/files/{file_id}

client.files.retrieve(file_id=file_id)
# FileObject(
# id='clg681objj8g9m7n4je0',
# bytes=761790,
# created_at=1700815879,
# filename='xlnet.pdf',
# object='file',
# purpose='file-extract',
# status='ok', status_details='') # status 如果为 error 则抽取失败

    本功能支持获取指定文件的文件抽取结果。通常的，它是一个合法的 JSON 格式的 string，并且对齐了我们的推荐格式。 如需抽取多个文件，您可以在某个 message 中用换行符 \n 隔开，拼接为一个大字符串，role 设置为 system 的方式加入历史记录。

GET https://api.moonshot.cn/v1/files/{file_id}/content

# file_content = client.files.retrieve_content(file_id=file_object.id)
# type of file_content is `str`
# 注意，之前 retrieve_content api 在最新版本标记了 warning, 可以用下面这行代替
# 如果是旧版本，可以用 retrieve_content
file_content = client.files.content(file_id=file_object.id).text
# 我们的输出结果目前是一个内部约定好格式的 json, 但是在 message 中应该以 text 格式放进去

Last updated on 2025年7月29日
Partial Mode
上下文缓存

文件接口 - Moonshot AI 开放平台 - Kimi 大模型 API 服务

🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    调用示例
    创建 Cache
    请求参数
    返回参数
    列举 Cache
    请求参数
    删除 Cache
    更新 Cache
    请求参数
    查询 Cache
    验证并使用 Cache
    🫵 关于使用缓存的重要提示
    通过 Headers 使用缓存
    🍬 通过 Message Content 使用缓存
    Context Caching 标签系统
    应用场景
    创建 Tag
    列举 Tag
    删除 Tag
    获取 Tag 信息
    使用 Tag

文档
API 文档
上下文缓存
上下文缓存接口

Context Caching （上下文缓存）是一种高效的数据管理技术，它允许系统预先存储那些可能会被频繁请求的大量数据或信息。这样，当您再次请求相同信息时，系统可以直接从缓存中快速提供，而无需重新计算或从原始数据源中检索，从而节省时间和资源。

目前 kimi-latest 模型和 kimi-k2-0711-preview 模型支持自动 Cache 上下文缓存，无需通过下面的上下文缓存接口来创建 Cache 使用，具体的价格详见模型推理输入价格（缓存命中）列。

我们将改造上一章节中的文件上传示例，使用 Context Caching 技术完成文件对话功能，完整的代码如下：

from typing import *
 
import os
import json
from pathlib import Path
 
import httpx
from openai import OpenAI
 
client = OpenAI(
    base_url="https://api.moonshot.cn/v1",
    # 我们会从环境变量中获取 MOONSHOT_DEMO_API_KEY 的值作为 API Key，
    # 请确保你已经在环境变量中正确设置了 MOONSHOT_DEMO_API_KEY 的值
    api_key=os.environ["MOONSHOT_DEMO_API_KEY"],
)
 
 
def upload_files(files: List[str], cache_tag: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    upload_files 会将传入的文件（路径）全部通过文件上传接口 '/v1/files' 上传，并获取上传后的
    文件内容生成文件 messages。每个文件会是一个独立的 message，这些 message 的 role 均为
    system，Kimi 大模型会正确识别这些 system messages 中的文件内容。
 
    如果你设置了 cache_tag 参数，那么 upload_files 还会将你上传的文件内容存入 Context Cache
    上下文缓存中，后续你就可以使用这个 Cache 来对文件内容进行提问。当你指定了 cache_tag 的值时，
    upload_files 会生成一个 role 为 cache 的 message，通过这个 message，你可以引用已被缓存
    的文件内容，这样就不必每次调用 `/v1/chat/completions` 接口时都要把文件内容再传输一遍。
 
    注意，如果你设置了 cache_tag 的值，你需要把 upload_files 返回的 messages 放置在请求
    `/v1/chat/completions` 接口时 messages 参数列表的第一位（实际上，我们推荐不管是否启用
    cache_tag，都将 upload_files 返回的 messages 放置在 messages 列表的头部）。
 
    关于 Context Caching 的具体信息，可以访问这里：
 
    https://platform.moonshot.cn/docs/api/caching
 
    :param files: 一个包含要上传文件的路径的列表，路径可以是绝对路径也可以是相对路径，请使用字符串
        的形式传递文件路径。
    :param cache_tag: 设置 Context Caching 的 tag 值，你可以将 tag 理解为自定义的 Cache 名称，
        当你设置了 cache_tag 的值，就意味着启用 Context Caching 功能，默认缓存时间是 300 秒，每次
        携带缓存进行 `/v1/chat/completions` 请求都将刷新缓存存活时间（300 秒）。
    :return: 一个包含了文件内容或文件缓存的 messages 列表，请将这些 messages 加入到 Context 中，
        即请求 `/v1/chat/completions` 接口时的 messages 参数中。
    """
    messages = []
 
    # 对每个文件路径，我们都会上传文件并抽取文件内容，最后生成一个 role 为 system 的 message，并加入
    # 到最终返回的 messages 列表中。
    for file in files:
        file_object = client.files.create(file=Path(file), purpose="file-extract")
        file_content = client.files.content(file_id=file_object.id).text
        messages.append({
            "role": "system",
            "content": file_content,
        })
 
    if cache_tag:
        # 当启用缓存（即 cache_tag 有值时），我们通过 HTTP 接口创建缓存，缓存的内容则是前文中通过文件上传
        # 和抽取接口生成的 messages 内容，我们为这些缓存设置一个默认的有效期 300 秒（通过 ttl 字段），并
        # 为这个缓存打上标记，标记值为 cache_tag（通过 tags 字段）。
        r = httpx.post(f"{client.base_url}caching",
                       headers={
                           "Authorization": f"Bearer {client.api_key}",
                       },
                       json={
                           "model": "moonshot-v1",
                           "messages": messages,
                           "ttl": 300,
                           "tags": [cache_tag],
                       })
 
        if r.status_code != 200:
            raise Exception(r.text)
 
        # 创建缓存成功后，我们不再需要将文件抽取后的内容原封不动地加入 messages 中，取而代之的是，我们可以设置一个
        # role 为 cache 的消息来引用我们已缓存的文件内容，只需要在 content 中指定我们给 Cache 设定的 tag 即可，
        # 这样可以有效减少网络传输的开销，即使是多个文件内容，也只需要添加一条 message，保持 messages 列表的清爽感。
        return [{
            "role": "cache",
            "content": f"tag={cache_tag};reset_ttl=300",
        }]
    else:
        return messages
 
 
def main():
    file_messages = upload_files(
        files=["upload_files.py"],
        # 你可以取消下方行的注释，来体验通过 Context Caching 引用文件内容，并根据文件内容向 Kimi 提问。
        # cache_tag="upload_files",
    )
 
    messages = [
        # 我们使用 * 语法，来解构 file_messages 消息，使其成为 messages 列表的前 N 条 messages。
        *file_messages,
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，"
                       "准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不"
                       "可翻译成其他语言。",
        },
        {
            "role": "user",
            "content": "总结一下这些文件的内容。",
        },
    ]
 
    print(json.dumps(messages, indent=2, ensure_ascii=False))
 
    completion = client.chat.completions.create(
        model="moonshot-v1-128k",
        messages=messages,
    )
 
    print(completion.choices[0].message.content)
 
 
if __name__ == '__main__':
    main()
 

在这个例子中，我们为上一章节的文件上传示例添加了 Context Caching 选项，你可以通过设置 cache_tag 来启用 Context Caching，并在后续对话中通过 cache_tag 引用缓存内容，通过引用的方式，你不再需要将文件内容添加至 messages 列表中，同时，Context Caching 技术还会大幅度降低对于相同文件内容多次提问的调用成本。

POST https://api.moonshot.cn/v1/caching
参数名称	参数类型（以 Python Type Hint 为例）	是否必填	参数说明
model	str	是	模型组（model family）名称。注意，由于被缓存的内容可同时应用于 moonshot 的多个模型（moonshot-v1-8k、moonshot-v1-32k、moonshot-v1-128k），因此这里不指定某个具体的模型，而是指定模型的组名称；当前支持的值为 moonshot-v1。
messages	List[Dict[str, any]]	是	缓存的消息内容，其格式与 /v1/chat/completions 接口中的 messages 一致（使用相同的校验规则），支持所有类型的消息（role=[system, user, tool, assistant]，支持 name、tool_call_id、tool_calls 参数，不支持 partial 参数）。此外，当你的 messages 包含 role 为 tool 的消息时，请确保在 role=tool 的消息前正确放置了携带 tool_calls 参数的 assistant message，并确保该 assistant message 中的所有 tool_calls 均被正确放置在 messages 中，否则会导致缓存创建失败。
tools	List[Dict[str, any]]	否	缓存的工具内容，其格式与 /v1/chat/completions 接口中的 tools 一致（使用相同的校验规则）。工具列表可以为空（此时你需要保证 messages 字段为合法值）。此外，当你的 messages 包含携带 tool_calls 参数的 assistant message 时，请确保该 tool_calls 中的所有 tools 均已由 tools 参数正确提供，否则会导致缓存创建失败。
name	str	否	缓存名称，这是一个辅助性质的字段，可以使用与你业务相关的信息来设置本次缓存的名称。
description	str	否	缓存描述信息，这是一个辅助性质的字段，可以使用与你业务相关的信息来设置本次缓存的描述信息，在检索缓存时，你可以通过 description 字段来判断这个缓存是否是你需要的缓存。
metadata	List[Dict[str, str]]	否	缓存的元信息，你可以将与你业务相关的各种信息以 key-value 的形式存储在 metadata 中，你最多可以设置 16 组元信息，每组元信息的 key 长度最高不超过 64 个 utf-8 字符，每组元信息的 value 长度最高不超过 512 个 utf-8 字符。
expired_at	int	是（expired_at 与 ttl 参数必须指定其中的一个值）	缓存的过期时间，格式为 unix 时间戳（单位为秒），是指缓存过期的某个具体时间点（而不是时间段）。注意，请确保 expired_at 字段的值大于服务器接收到创建缓存请求时当前时间戳的值，否则会导致缓存创建失败。推荐的做法是，使用当前时间戳加上你希望缓存存活的时间（秒为单位）作为 expired_at 字段的值。额外的，如果不设置 expired_at 或该值为 0，我们会为缓存设置一个默认的过期时间，当前为 1 小时。expired_at 字段的值最大不超过服务器接收到创建缓存请求的时间点加 3600 秒。当使用 expired_at 参数时，请勿指定 ttl 参数。
ttl	int		缓存的有效期，单位为秒，指的是从当前服务器接收到请求的时间点开始，该缓存的存活时间。当使用 ttl 参数时，请勿指定 expired_at 参数。其与 expired_at 的关系为：expired_at = now() + ttl

注：当前版本，对于单个用户，创建的单个缓存大小最大为 128k，如果请求中的 messages 和 tools 字段包含的 Tokens 数量超过 128k，将会导致缓存创建失败。

以下为一个正确的请求示例：

{
    "model": "moonshot-v1",
    "messages": [
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"
        },
        { "role": "user", "content": "你好，我叫李雷，1+1等于多少？" }
    ],
    "tools": [
        {
          "type": "function",
          "function": {
            "name": "CodeRunner",
            "description": "代码执行器，支持运行 python 和 javascript 代码",
            "parameters": {
              "properties": {
                "language": {
                  "type": "string",
                  "enum": ["python", "javascript"]
                },
                "code": {
                  "type": "string",
                  "description": "代码写在这里"
                }
              },
              "type": "object"
            }
          }
        }
    ],
    "name": "The name of the cache. Optional. The maximum length is 256 characters",
    "description": "The description of the assistant. Optional. The maximum length is 512 characters.",
    "metadata": {
      "biz_id": "110998541001"
    },
    "expired_at": 1718680442
}

对于上述请求，/v1/caching 接口将返回：

{
    "id": "cache-id-xxxxxxxxxxxxx",
    "status": "pending",
    "object": "context-cache",
    "created_at": 1699063291,
    "tokens": 32768, 
    "expired_at": 1718680442,
    "model": "moonshot-v1",
    "messages": [
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"
        },
        { "role": "user", "content": "你好，我叫李雷，1+1等于多少？" }
    ],
    "tools": [
        {
          "type": "function",
          "function": {
            "name": "CodeRunner",
            "description": "代码执行器，支持运行 python 和 javascript 代码",
            "parameters": {
              "properties": {
                "language": {
                  "type": "string",
                  "enum": ["python", "javascript"]
                },
                "code": {
                  "type": "string",
                  "description": "代码写在这里"
                }
              },
              "type": "object"
            }
          }
        }
    ],
    "name": "The name of the cache. Optional. The maximum length is 256 characters",
    "description": "The description of the assistant. Optional. The maximum length is 512 characters.",
    "metadata": {
      "biz_id": "110998541001"
    }
}

注：返回值中的model、messages、tools、name、description、metadata参数与创建缓存时的请求参数相同，故在此省略。
参数名称	参数类型（以 Python Type Hint 为例）	参数说明
id	str	缓存 id，使用这个 id 执行对缓存的 Modify、Retrieve 操作，或是在 /v1/chat/completions 接口中携带这个 id 以应用缓存。
status	Literal["pending", "ready", "error", "inactive"]	当前缓存的状态，其遵循以下规则：1. 当缓存被初次创建时，其初始状态为 pending；2. 如果参数合法，缓存创建成功，其状态变更为 ready；3. 如果参数不合法，或因其他原因缓存创建失败，其状态变更为 error；4. 对于已过期的缓存，其状态变更为 inactive；5. 更新一个存在 id 的缓存，其状态会重新回到 pending，并应用上述步骤 2、3、4；
object	str	当前缓存的存储类型，为固定值 context-cache
created_at	int	当前缓存的创建时间
expired_at	int	当前缓存的过期时间
tokens	int	当前已缓存的 Tokens 数量。注意，已缓存的 Tokens 数量并不总是等于最终在 /v1/chat/completions 接口中消耗的 Tokens 数量，这是因为在调用 /v1/chat/completions 接口时会使用不同的模型（这会影响 Tokens 计算），最终的 Tokens 数以 /v1/chat/completions 接口返回的 Usages 信息为准。
error	Dict[str, str]	当缓存创建失败，即 status 字段为“error”时，会额外携带一个 error 字段，用于表示缓存创建失败的具体失败原因。其具体格式为：{ "type": "error_type", "message": "error_message"}

GET https://api.moonshot.cn/v1/caching?limit=20&order=desc&after=cache-id-xxxxxx&metadata[biz_id]=110998541001

注：请求参数以 URL 查询参数的形式提供
参数名称	参数类型（以 Python Type Hint 为例）	是否必填	参数说明
limit	int	否	指当前请求单页返回的缓存数量，默认值为 20
order	Literal["asc", "desc"]	否	指当前请求时查询缓存的排序规则，按缓存的 created_at 进行排序，默认值为 desc。
after	str	否	指当前请求时，应该从哪一个缓存开始进行查找，其值为缓存 id；注意，查询结果不包含 after 指定的那个缓存。
before	str	否	指当前请求时，应该查询到哪一个缓存为止，其值为缓存 id；注意，查询结果不包含 before 指定的那个缓存。
metadata	Dict[str, str]	否	指当前请求时，用于筛选缓存的 metadata 信息，你可以使用 metadata 来快速使用你的业务信息筛选需要的缓存。使用形如 metadata[key]=value 的形式来传递参数。

以下为一次正确请求返回的内容：

{
    "object": "list",
    "data": [
       {
            "id": "cache-id-xxxxxxxxxxxxx",
            "status": "pending",
            "object": "context-cache",
            "created_at": 1699063291,
            "tokens": 32768, 
            "expired_at": 1718680442,
            "model": "moonshot-v1",
            "messages": [
                {
                    "role": "system",
                    "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"
                },
                { "role": "user", "content": "你好，我叫李雷，1+1等于多少？" }
            ],
            "tools": [
                {
                  "type": "function",
                  "function": {
                    "name": "CodeRunner",
                    "description": "代码执行器，支持运行 python 和 javascript 代码",
                    "parameters": {
                      "properties": {
                        "language": {
                          "type": "string",
                          "enum": ["python", "javascript"]
                        },
                        "code": {
                          "type": "string",
                          "description": "代码写在这里"
                        }
                      },
                      "type": "object"
                    }
                  }
                }
            ],
            "name": "The name of the cache. Optional. The maximum length is 256 characters",
            "description": "The description of the assistant. Optional. The maximum length is 512 characters.",
            "metadata": {
              "biz_id": "110998541001"
            }
        }
    ]
}

DELETE https://api.moonshot.cn/v1/caching/{{cache-id}}

删除缓存：

    如果成功删除已有缓存，则会返回 HTTP 200，并产生一个形如这样的响应信息：

{
    "deleted": true,
    "id": "cache-xxxxxxxxxxxxxxxxxxxx",
    "object": "context_cache_object.deleted"
}

    如果指定的缓存 id 不存在，则会返回 HTTP 404 Not Found；

PUT https://api.moonshot.cn/v1/caching/{{cache-id}}

参数名称	参数类型（以 Python Type Hint 为例）	是否必填	参数说明
metadata	List[Dict[str, str]]	否	缓存的元信息，你可以将与你业务相关的各种信息以 key-value 的形式存储在 metadata 中，你最多可以设置 16 组元信息，每组元信息的 key 长度最高不超过 64 个 utf-8 字符，每组元信息的 value 长度最高不超过 512 个 utf-8 字符。
expired_at	int	否（expired_at 与 ttl 参数最多指定其中的一个值）	缓存的过期时间，格式为 unix 时间戳（单位为秒），是指缓存过期的某个具体时间点（而不是时间段）。注意，请确保 expired_at 字段的值大于服务器接收到创建缓存请求时当前时间戳的值，否则会导致缓存创建失败。推荐的做法是，使用当前时间戳加上你希望缓存存活的时间（秒为单位）作为 expired_at 字段的值。额外的，如果不设置 expired_at 或该值为 0，我们会为缓存设置一个默认的过期时间，当前为 1 小时。expired_at 字段的值最大不超过服务器接收到创建缓存请求的时间点加 3600 秒。当使用 expired_at 参数时，请勿指定 ttl 参数。
ttl	int	否（expired_at 与 ttl 参数最多指定其中的一个值）	缓存的有效期，单位为秒，指的是从当前服务器接收到请求的时间点开始，该缓存的存活时间。当使用 ttl 参数时，请勿指定 expired_at 参数。其与 expired_at 的关系为：expired_at = now() + ttl

以下为一个正确的请求示例：

{
  "metadata": {
    "biz_id": "110998541001"
  },
  "expired_at": 1718680442
}

成功调用更新缓存接口将会返回与创建缓存接口相同的响应内容。

GET https://api.moonshot.cn/v1/caching/{{cache-id}}

查询缓存：

    缓存 id 存在的场合，返回该 id 对应的缓存信息，其内容与创建缓存接口返回的响应一致；
    若缓存 id 不存在，则返回 HTTP 404 Not Found；

缓存将会被应用在 /v1/chat/completions 接口中。

当你在调用 /v1/chat/completions 接口时指定了一个合法的尚未过期的缓存 id 的场合，我们不保证这个缓存 id 对应的缓存一定会被启用，在某些特殊场合会出现无法使用缓存的情况，在这种情况下，请求仍然会成功，内容也会正确返回；但当缓存未被启用时，当前请求的 Tokens 及对应的费用均会以 /v1/chat/completions 接口的标准资费信息进行计算和扣减。

为了最大限度地减少对接口、SDK 兼容性的破坏，我们将会通过 HTTP Headers 来验证、使用缓存、以及调整缓存相关规则。

通过 Headers 调用缓存的场合，你必须：

    保证调用 /v1/chat/completions 时的 messages 的前 N 个 messages 与缓存的所有 messages 完全一致（N ＝ 缓存的 messages 长度），包括 messages 的顺序，message 中的字段值都需要保持一致，否则会导致无法命中缓存；
    保证调用 /v1/chat/completions 时的 tools 与缓存的 tools 完全一致，否则将会导致无法命中缓存；

注 1：我们会使用 Hash 来校验请求 /v1/chat/completions 接口的 messages 前缀与缓存的 messages 是否一致、以及请求的 tools 与缓存的 tools 是否一致，因此请务必保证这两者与缓存内容保持完全一致。

注 2：在通过 Headers 使用缓存的场合，即使消息已经被缓存，你也必须在请求 /v1/chat/completions 时再次携带这些消息。
Headers 名称	是否必填	Headers 说明
X-Msh-Context-Cache	是	通过设置该 Header 来指定当前请求所使用的缓存，其值为缓存 id，只有设置了该值才会启用缓存。
X-Msh-Context-Cache-DryRun	否	通过设置该 Header 来指定仅验证缓存是否生效，而不执行推理过程，其值为 Literal[1, 0]；如果值为 1，则只验证缓存是否生效，而不执行推理过程，也不消耗任何 Tokens。
X-Msh-Context-Cache-Reset-TTL	否	通过设置该值来自动延长缓存的 expired_at 过期时间，其值为单位为秒的时间段整数；如果设置了该值，则每次成功调用 /v1/chat/completions 接口都会为该缓存设置新的有效期，新的有效期时间为服务器接收到请求的时间点加上该 Header 指定的值。例如，当 TTL 设置为 3600 时，每次请求成功，都会将缓存的过期时间置为 now() + 3600，而非 expired_at + 3600。额外的，如果当前缓存已过期，在设置了该 Header 的场合，会重新启用该缓存，并将其状态设置为 pending 并更新其 expired_at。
Headers 名称	Headers 说明
Msh-Context-Cache-Id	当前请求所使用的缓存 id
Msh-Context-Cache-Token-Saved	当前请求由于使用了缓存所节省的 Tokens 数量
Msh-Context-Cache-Token-Exp	当前缓存的过期时间，即 expired_at

以下是一个使用缓存调用 /v1/chat/completions 的正确示例：

POST https://api.moonshot.cn/v1/chat/completions
 
Content-Type: application/json; charset=utf-8
Content-Length: 6418
X-Msh-Context-Cache: cache-id-xxxxxxxxxxxxxxx
X-Msh-Context-Cache-Reset-TTL: 3600
 
{
    "model": "moonshot-v1-128k",
    "messages": [
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"
        },
        { "role": "user", "content": "你好，我叫李雷，1+1等于多少？" }
    ],
    "tools": [
        {
          "type": "function",
          "function": {
            "name": "CodeRunner",
            "description": "代码执行器，支持运行 python 和 javascript 代码",
            "parameters": {
              "properties": {
                "language": {
                  "type": "string",
                  "enum": ["python", "javascript"]
                },
                "code": {
                  "type": "string",
                  "description": "代码写在这里"
                }
              },
              "type": "object"
            }
          }
        }
    ]
}

from openai import OpenAI
 
client = OpenAI(
    api_key = "$MOONSHOT_API_KEY",
    base_url = "https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model = "moonshot-v1-8k",
    messages = [
        {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
        {"role": "user", "content": "你好，我叫李雷，1+1等于多少？"}
    ],
    temperature = 0.3,
    extra_headers={
        "X-Msh-Context-Cache": "cache-xxx-xxx-xxx",
        "X-Msh-Context-Cache-Reset-TTL": "3600",
    },
)
 
print(completion.choices[0].message.content)

你可以将形如以下形式的 message 置于你的 messages 列表的首位以使用缓存：

{
    "role": "cache",
    "content": "cache_id=xxx;other-options"
}

这是一个特殊的 role=cache 的消息，它通过 content 字段指定需要使用哪个缓存：

    你必须把这个消息放在 messages 列表的第一位；
    tools 参数必须为 null（空数组也将视为有值）；
    我们会将这条特殊的消息替换为你已经缓存的消息列表，并将缓存中的 tools 填充到 tools 参数中；
    你不需要在 messages 中放置已经被缓存的消息，你只需要添加那些没有被缓存的 messages 即可；
    你可以将这条特殊的消息视作引用已缓存的消息列表；
    对于 content，其规则如下：
    使用 cache_id={id} 的形式来指定缓存 id；
    通过 reset_ttl={ttl} 的形式来指定是否重新设置 expired_at 过期时间；
    通过 dry_run=1 的形式来指定是否仅校验缓存而不启用推理过程；
    以上参数通过分号 ; 进行拼接，最后一个参数后请不要再放置一个额外的分号；
    其中：

    cache_id 对应 Headers 中的 X-Msh-Context-Cache；
    dry_run 对应 Headers 中的 X-Msh-Context-Cache-DryRun；
    reset_ttl 对应 Headers 中的 X-Msh-Context-Cache-Reset-TTL；
    参数值及规则也与 Headers 保持一致；

我们为 Context Caching 添加了一套标签系统，用于通过标签管理和使用 Context Cache。

在使用 Context Caching 的过程中，如果想对已缓存的内容进行修改（例如有新的知识需要加入的上下文中，或是某些时效性强的数据需要更新），我们推荐的做法是删除原 Cache，再使用更新后的内容创建新 Cache。这一过程会导致 Context Cache 的 ID 发生变化，在实际开发中，可能需要开发者编写一些额外的代码来管理 Cache，例如通过业务上自定义的 key 与 cache_id 进行匹配映射，这无疑会增加开发者在使用 Cache 时的心智负担。

因此我们设计了 Context Caching 标签系统（Tag），试图降低 Context Caching 使用过程中的心智负担。你可以为 Context Cache 打上任意多的标签，并通过在 Message 中指定 tag 名称来使用其对应的 Cache。使用标签的一个好处在于，标签完全由开发者决定，并且不会随着 Cache 变动而发生变化（它的反面，cache_id 是会随着缓存变动而发生变化的）。

POST https://api.moonshot.cn/v1/caching/refs/tags

参数名称	参数类型（以 Python Type Hint 为例）	是否必填	参数说明
tag	str	是	标签名称，长度最小值为 1，最大值为 128，首字符必须为大小写字母，非首字符可以使用大小写字母、下划线、减号及英文句号。
cache_id	str	是	已经创建成功的缓存 id

你也可以将创建 Tag 接口理解为，为已经存在的 Cache 绑定一个 Tag。

以下为一次正确请求返回的内容：

{
    "cache_id": "cache-et3tmxxkzr7i11dp6x51",
    "created_at": 1719976735,
    "object": "cache_object.tag",
    "owned_by": "cn0psxxcp7fclnphkcpg",
    "tag": "my-tag"
}
 

GET https://api.moonshot.cn/v1/caching/refs/tags?limit=20&order=desc&after=tag-id-xxxxxx

注：请求参数以 URL 查询参数的形式提供
参数名称	参数类型（以 Python Type Hint 为例）	是否必填	参数说明
limit	int	否	指当前请求单页返回的标签数量，默认值为 20
order	Literal["asc", "desc"]	否	指当前请求时查询标签的排序规则，按标签的 created_at 进行排序，默认值为 desc。
after	str	否	指当前请求时，应该从哪一个标签开始进行查找，其值为标签 tag；注意，查询结果不包含 after 指定的那个标签。
before	str	否	指当前请求时，应该查询到哪一个标签为止，其值为标签 tag；注意，查询结果不包含 before 指定的那个标签。

以下为一次正确请求返回的内容：

{
  "object": "list",
  "data": [
    {
      "tag": "tom",
      "cache_id": "cache-et3w5r7e13sqw5wtzsei",
      "object": "cache_object.tag",
      "owned_by": "root",
      "created_at": 1719910897
    }
  ]
}

DELETE https://api.moonshot.cn/v1/caching/refs/tags/{{your_tag_name}}

注：不论 Tag 是否存在，删除 Tag 接口都将返回成功。

以下为一次正确请求返回的内容：

{
  "deleted": true,
  "object": "cache_object.tag.deleted",
  "tag": "tom"
}

GET https://api.moonshot.cn/v1/caching/refs/tags/{{your_tag_name}}

以下为一次正确请求返回的内容：

{
    "cache_id": "cache-et3tmxxkzr7i11dp6x51",
    "created_at": 1719976735,
    "object": "cache_object.tag",
    "owned_by": "cn0psxxcp7fclnphkcpg",
    "tag": "my-tag"
}
 

GET https://api.moonshot.cn/v1/caching/refs/tags/{{your_tag_name}}/content

注：这是一个快速查看 Tag 标记的 Cache 信息的接口，其等价于先调用 /v1/caching/refs/tags/{{your_tag_name}}，再调用 /v1/caching/{{tag.cache_id}}，其返回值与 /v1/caching/{{cache-id}} 完全一致。

目前，我们仅支持通过 Message Content 使用标签，具体的用法是，使用 tag={tag} 的方式替代原 cache_id={id}，如下方所示：

{
    "role": "cache",
    "content": "tag=xxx;other-options"
}

Last updated on 2025年7月30日
文件接口
其它
已复制链接
已复制链接

上下文缓存接口 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    计算 Token
    请求地址
    请求内容
    调用示例
    返回内容
    查询余额
    请求地址
    调用示例
    返回内容
    返回内容说明

文档
API 文档
其它
其它接口

POST https://api.moonshot.cn/v1/tokenizers/estimate-token-count

estimate-token-count 的输入结构体和 chat completion 基本一致。

{
    "model": "kimi-k2-0711-preview",
    "messages": [
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"
        },
        { "role": "user", "content": "你好，我叫李雷，1+1等于多少？" }
    ]
}

字段	说明	类型	取值
messages	包含迄今为止对话的消息列表。	List[Dict]	这是一个结构体的列表，每个元素类似如下：json{"role": "user", "content": "你好"} role 只支持 system,user,assistant 其一，content 不得为空
model	Model ID， 可以通过 List Models 获取	string	目前是 kimi-k2-0711-preview,moonshot-v1-8k,moonshot-v1-32k,moonshot-v1-128k, moonshot-v1-auto,kimi-latest,moonshot-v1-8k-vision-preview,moonshot-v1-32k-vision-preview,moonshot-v1-128k-vision-preview,kimi-thinking-preview其一

curl 'https://api.moonshot.cn/v1/tokenizers/estimate-token-count' \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $MOONSHOT_API_KEY" \
  -d '{
    "model": "kimi-k2-0711-preview",
    "messages": [
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"
        },
        {
            "role": "user",
            "content": "你好，我叫李雷，1+1等于多少？"
        }
    ]
}'

{
    "data": {
        "total_tokens": 80
    }
}

当没有 error 字段，可以取 data.total_tokens 作为计算结果

GET https://api.moonshot.cn/v1/users/me/balance

curl https://api.moonshot.cn/v1/users/me/balance -H "Authorization: Bearer $MOONSHOT_API_KEY"

{
  "code": 0,
  "data": {
    "available_balance": 49.58894,
    "voucher_balance": 46.58893,
    "cash_balance": 3.00001
  },
  "scode": "0x0",
  "status": true
}

字段	说明	类型	单位
available_balance	可用余额，包括现金余额和代金券余额, 当它小于等于 0 时, 用户不可调用推理 API	float	人民币元（CNY）
voucher_balance	代金券余额, 不会为负数	float	人民币元（CNY）
cash_balance	现金余额, 可能为负数, 代表用户欠费, 当它为负数时, available_balance 为 voucher_balance 的值	float	人民币元（CNY）
Last updated on 2025年7月29日
上下文缓存
🎉 促销活动

其它接口 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    关于 API 兼容性
    temperature 和 N 值
    stream 模式下的 usage 值
    已被废弃的 function_call
    关于 tool_choice

文档
入门指南
从 OpenAI 迁移到 Kimi API
从 openai 迁移到 Kimi API

Kimi API 兼容了 OpenAI 的接口规范，你可以使用 OpenAI 提供的 Python
或 NodeJS

SDK 来调用和使用 Kimi 大模型，这意味着如果你的应用和服务基于 openai 的模型进行开发，那么只需要将 base_url 和 api_key 替换成 Kimi 大模型的配置，即可无缝将你的应用和服务迁移至使用 Kimi 大模型，代码示例如下：

from openai import OpenAI
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY", # <--在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1", # <-- 将 base_url 从 https://api.openai.com/v1 替换为 https://api.moonshot.cn/v1
)

我们会尽力保证 Kimi API 与 OpenAI 的兼容性，但在某些特殊场合，Kimi API 与 OpenAI 仍然存在一些差异和不同（但这不影响整体兼容性），我们将详细阐述 Kimi API 与 OpenAI 的不同点，并提出可行的迁移解决方案以帮助开发者顺利完成迁移工作。

以下是与 OpenAI 兼容的接口列表：

    /v1/chat/completions
    /v1/files
    /v1/files/{file_id}
    /v1/files/{file_id}/content

当你使用 OpenAI 的接口时，你可以同时设置 temperature=0 和 n>1，即在 temperature 值为 0 的场合，同时返回多个不同的回答（即 choices）。

然而在 Kimi API 中，当你将 temperature 的值设置为 0 或接近 0 时（例如 0.001），我们将只能提供 1 个回答（即 len(choices)=1，如果你在把 temperature 设置为 0 的同时，使用了一个大于 1 的 n 值，我们将返回一个“非法请求”错误，即 invalid_request_error。

额外的，请注意：Kimi API 的 temperature 参数的取值范围是 [0, 1]，而 OpenAI 的 temperature 参数的取值范围是 [0, 2]。

迁移建议：我们推荐的 temperature 值为 0.3，kimi-k2-0711-preview模型建议设置值为 0.6，如果你的业务场景需要通过设置 temperature=0 来让 Kimi 大模型输出比较稳定的结果，那么请额外注意将 n 值设置为 1，或不设置 n 值（此时会使用默认的 n=1 作为请求参数，这是合法的）。

当你使用 OpenAI 的 chat.completions 接口时，在流式输出（即 stream=True）的场合下，输出结果默认不包含 usage 用量信息（包括 prompt_tokens/completion_tokens/total_tokens），OpenAI 提供了一个额外的参数 stream_options={"include_usage": True} 来使返回的最后一个数据块包含 usage 信息。

在 Kimi API 中，我们除了 stream_options={"include_usage": True} 参数外，还会在每个 choice 的结束数据块中放置 usage 信息（包括 prompt_tokens/completion_tokens/total_tokens，以及如果你使用了 Context Caching 技术，还会包含 cached_tokens）。

迁移建议：通常情况下，开发者不需要做任何额外的兼容性举措，如果你的业务场景需要统计每个 choice 各自的 usage 信息，可以访问 choice.usage 字段，注意：在不同的 choices 中，仅有 usage.completion_tokens 和 usage.total_tokens 字段的值是不同的，choices 们拥有相同的 usage.prompt_tokens 和 usage.cached_tokens 值。

OpenAI 在 2023 年提供了 functions 参数以开启函数调用（即 function_call）功能。经过功能迭代，OpenAI 后续推出了工具调用（即 tool_calls）功能，并将 functions 参数标记为已废弃（deprecated），这意味着在后续的 API 迭代中，functions 参数随时可能被移除。

Kimi API 完整支持了工具调用（即 tool_calls）的能力，同时，由于 functions 已被废弃，Kimi API 不支持使用 functions 参数执行函数调用。

迁移建议：如果你的应用或服务依赖于工具调用（即 tool_calls），那么不需要做任何额外的兼容性举措；如果你的应用或服务依赖于已经废弃的函数调用（即 function_call），我们建议你迁移至工具调用（即 tool_calls），工具调用拓展了函数调用的能力，同时支持函数并行调用，关于工具调用的具体示例，可以参考我们的工具调用指南：

使用 Kimi API 完成工具调用（tool_calls）

下面是一个从 functions 迁移至 tools 的示例：

我们会将需要改造的部分代码以注释的形式呈现，并附上说明，以便于开发者能更好理解如何进行迁移。

from typing import *
 
import json
import httpx
from openai import OpenAI
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY",  # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)
 
functions = [
    {
        "name": "search",  # 函数的名称，请使用英文大小写字母、数据加上减号和下划线作为函数名称
        "description": """ 
            通过搜索引擎搜索互联网上的内容。
 
            当你的知识无法回答用户提出的问题，或用户请求你进行联网搜索时，调用此工具。请从与用户的对话中提取用户想要搜索的内容作为 query 参数的值。
            搜索结果包含网站的标题、网站的地址（URL）以及网站简介。
        """,  # 函数的介绍，在这里写上函数的具体作用以及使用场景，以便 Kimi 大模型能正确地选择使用哪些函数
        "parameters": {  # 使用 parameters 字段来定义函数接收的参数
            "type": "object",  # 固定使用 type: object 来使 Kimi 大模型生成一个 JSON Object 参数
            "required": ["query"],  # 使用 required 字段告诉 Kimi 大模型哪些参数是必填项
            "properties": {  # properties 中是具体的参数定义，你可以定义多个参数
                "query": {  # 在这里，key 是参数名称，value 是参数的具体定义
                    "type": "string",  # 使用 type 定义参数类型
                    "description": """
                        用户搜索的内容，请从用户的提问或聊天上下文中提取。
                    """  # 使用 description 描述参数以便 Kimi 大模型更好地生成参数
                }
            }
        }
    }
]
 
 
def search_impl(query: str) -> List[Dict[str, Any]]:
    """
    search_impl 使用搜索引擎对 query 进行搜索，目前主流的搜索引擎（例如 Bing）都提供了 API 调用方式，你可以自行选择
    你喜欢的搜索引擎 API 进行调用，并将返回结果中的网站标题、网站链接、网站简介信息放置在一个 dict 中返回。
 
    这里只是一个简单的示例，你可能需要编写一些鉴权、校验、解析的代码。
    """
    r = httpx.get("https://your.search.api", params={"query": query})
    return r.json()
 
 
def search(arguments: Dict[str, Any]) -> Any:
    query = arguments["query"]
    result = search_impl(query)
    return {"result": result}
 
 
function_map = {
    "search": search,
}
 
# ==========================================================================================================================================================
# tools 是 functions 的超集，因此我们可以通过已经定义的 functions 构造 tools，我们循环遍历每一个 function，并为其构造相应的 tool 格式；
# 同时，我们也生成相应的 tool_map。
# ==========================================================================================================================================================
 
tools = []
tool_map = {}
for function in functions:
    tool = {
        "type": "function",
        "function": function,
    }
    tools.append(tool)
    tool_map[function["name"]] = function_map[function["name"]]
 
messages = [
    {"role": "system",
     "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
    {"role": "user", "content": "请联网搜索 Context Caching，并告诉我它是什么。"}  # 在提问中要求 Kimi 大模型联网搜索
]
 
finish_reason = None
 
# ==========================================================================================================================================================
# 在这里，我们将 finish_reason 值判断由 function_call 修改成 tool_calls
# ==========================================================================================================================================================
# while finish_reason is None or finish_reason == "function_call":
while finish_reason is None or finish_reason == "tool_calls":
    completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=messages,
        temperature=0.6,
        # ==========================================================================================================================================================
        # 我们弃用 functions 参数，而是使用 tools 参数来启用工具调用
        # ==========================================================================================================================================================
        # function=functions,
        tools=tools,  # <-- 我们通过 tools 参数，将定义好的 tools 提交给 Kimi 大模型
    )
    choice = completion.choices[0]
    finish_reason = choice.finish_reason
 
    # ==========================================================================================================================================================
    # 在这里，我们将原先 function_call 的执行逻辑替换成 tool_calls 的执行逻辑；
    # 注意，由于 tool_calls 可能有多个，因此我们需要通过 for 循环逐个执行每个 tool_call。
    # ==========================================================================================================================================================
    # if finish_reason == "function_call":
    #   messages.append(choice.message)
    #   function_call_name = choice.message.function_call.name
    #   function_call_arguments = json.loads(choice.message.function_call.arguments)
    #   function_call = function_map[function_call_name]
    #   function_result = function_call(function_call_arguments)
    #   messages.append({
    #       "role": "function",
    #       "name": function_call_name,
    #       "content": json.dumps(function_result)
    #   })
 
    if finish_reason == "tool_calls":  # <-- 判断当前返回内容是否包含 tool_calls
        messages.append(choice.message)  # <-- 我们将 Kimi 大模型返回给我们的 assistant 消息也添加到上下文中，以便于下次请求时 Kimi 大模型能理解我们的诉求
        for tool_call in choice.message.tool_calls:  # <-- tool_calls 可能是多个，因此我们使用循环逐个执行
            tool_call_name = tool_call.function.name
            tool_call_arguments = json.loads(tool_call.function.arguments)  # <-- arguments 是序列化后的 JSON Object，我们需要使用 json.loads 反序列化一下
            tool_function = tool_map[tool_call_name]  # <-- 通过 tool_map 快速找到需要执行哪个函数
            tool_result = tool_function(tool_call_arguments)
 
            # 使用函数执行结果构造一个 role=tool 的 message，以此来向模型展示工具调用的结果；
            # 注意，我们需要在 message 中提供 tool_call_id 和 name 字段，以便 Kimi 大模型
            # 能正确匹配到对应的 tool_call。
            messages.append({
                "role": "tool",
                "tool_call_id": tool_call.id,
                "name": tool_call_name,
                "content": json.dumps(tool_result),  # <-- 我们约定使用字符串格式向 Kimi 大模型提交工具调用结果，因此在这里使用 json.dumps 将执行结果序列化成字符串
            })
 
print(choice.message.content)  # <-- 在这里，我们才将模型生成的回复返回给用户

Kimi API 支持 tool_choice 参数，但关于 tool_choice 参数的值与 OpenAI 有一些细微的差别。当前 Kimi API 与 OpenAI API 兼容的 tool_choice 值为：

    "none"
    "auto"
    null

请注意，当前版本的 Kimi API 暂时不支持 tool_choice=required 参数。

迁移建议：假如你的应用程序或服务依赖于 OpenAI API 中 tool_choice 字段的 required 值来确保大模型“一定”选择某个工具进行调用，我们建议使用一些特殊的手段来强化 Kimi 大模型对调用工具的认知以一定程度上兼容原有的业务逻辑，例如通过在提示词 prompt 中强调使用某个工具来达到类似的效果，我们通过简化版的代码来展示这一逻辑：

from openai import OpenAI
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY",  # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)
 
tools = {
    # 在这里定义你的 tools
}
 
messages = [
    # 在这里存储你的消息历史记录
]
 
completion = client.chat.completions.create(
    model="kimi-k2-0711-preview",
    messages=messages,
    temperature=0.6,
    tools=tools,
    # tool_choice="required",  # <-- 由于 Kimi API 暂时不支持 tool_choice=required，我们暂时屏蔽这一选项
)
 
choice = completion.choices[0]
if choice.finish_reason != "tool_calls":
    # 我们假定我们的业务逻辑能够确认此处必须要调用 tool_calls，
    # 在不使用 tool_choice=required 的情况下，我们通过提示词
    # prompt 来让 Kimi 强制选择一个工具进行调用
    messages.append(choice.message)
    messages.append({
        "role": "user",
        "content": "请选择一个工具（tool）来处理当前的问题。",  # 通常情况下，Kimi 大模型能理解调用工具的意图并选择一个工具进行调用
    })
    completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=messages,
        temperature=0.6,
        tools=tools,
    )
    choice = completion.choices[0]
    assert choice.finish_reason == "tool_calls"  # 这次的请求，理应返回 finish_reason=tool_calls
    print(choice.message.content)

需要注意的是，这种方式并不能保证百分之百成功触发 tool_calls，如果你的应用程序或服务对 tool_calls 的调用有非常非常强的依赖，请等待 Kimi API 的 tool_choice=required 特性上线。
Last updated on 2025年7月29日
常见问题
使用 API 调试工具

从 openai 迁移到 Kimi API - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    安装方式
    使用 go 命令安装
    从 Releases 页面下载
    使用方式
    启动服务
    检索请求
    导出请求

文档
入门指南
使用 API 调试工具
MoonPalace - Moonshot AI 月之暗面 Kimi API 调试工具

MoonPalace（月宫）是由 Moonshot AI 月之暗面提供的 API 调试工具。它具备以下特点：

    全平台支持：
        Mac
        Windows
        Linux；
    简单易用，启动后将 base_url 替换为 http://localhost:9988 即可开始调试；
    捕获完整请求，包括网络错误时的“事故现场”；
    通过 request_id、chatcmpl_id 快速检索、查看请求信息；
    一键导出 BadCase 结构化上报数据，帮助 Kimi 完善模型能力；

我们推荐在代码编写和调试阶段使用 MoonPalace 作为你的 API “供应商”，以便能快速发现和定位关于 API 调用和代码编写过程中的各种问题，对于 Kimi 大模型各种不符合预期的输出，你也可以通过 MoonPalace 导出请求详情并提交给 Moonshot AI 以改进 Kimi 大模型。

如果你已经安装了 go 工具链，你可以执行以下命令来安装 MoonPalace：

$ go install github.com/MoonshotAI/moonpalace@latest

上述命令会在你的 $GOPATH/bin/ 目录安装编译后的二进制文件，运行 moonpalace 命令来检查是否成功安装：

$ moonpalace
MoonPalace is a command-line tool for debugging the Moonshot AI HTTP API.
 
Usage:
  moonpalace [command]
 
Available Commands:
  cleanup     Cleanup Moonshot AI requests.
  completion  Generate the autocompletion script for the specified shell
  export      export a Moonshot AI request.
  help        Help about any command
  inspect     Inspect the specific content of a Moonshot AI request.
  list        Query Moonshot AI requests based on conditions.
  start       Start the MoonPalace proxy server.
 
Flags:
  -h, --help      help for moonpalace
  -v, --version   version for moonpalace
 
Use "moonpalace [command] --help" for more information about a command.

如果你仍然无法检索到 moonpalace 二进制文件，请尝试将 $GOPATH/bin/ 目录添加到你的 $PATH 环境变量中。

你可以从 Releases

页面下载编译好的二进制（可执行）文件：

    moonpalace-linux
    moonpalace-macos-amd64 => 对应 Intel 版本的 Mac
    moonpalace-macos-arm64 => 对应 Apple Silicon 版本的 Mac
    moonpalace-windows.exe

请根据自己的平台下载对应的二进制（可执行）文件，并将二进制（可执行）文件放置在已被包含在环境变量 $PATH 中的目录中，将其更名为 moonpalace，最后为其赋予可执行权限。

使用以下命令启动 MoonPalace 代理服务器：

$ moonpalace start --port <PORT>

MoonPalace 会在本地启动一个 HTTP 服务器，--port 参数指定 MoonPalace 监听的本地端口，默认值为 9988。当 MoonPalace 启动成功时，会输出：

[MoonPalace] 2024/07/29 17:00:29 MoonPalace Starts {'=>'} change base_url to "http://127.0.0.1:9988/v1"

按照要求，我们将 base_url 替换为显示的地址即可，如果你使用默认的端口，那么请设置 base_url=http://127.0.0.1:9988/v1，如果你使用了自定义的端口，请将 base_url 替换为显示的地址。

额外的，如果你想在调试时始终使用一个调试的 api_key，你可以在启动 MoonPalace 时使用 --key 参数为 MoonPalace 设定一个默认的 api_key，这样你就可以不用在请求时手动设置 api_key，MoonPalace 会帮你在请求 Kimi API 时添加你通过 --key 设定的 api_key。

如果你正确设置了 base_url，并成功调用 Kimi API，MoonPalace 会输出如下的信息：

$ moonpalace start --port <PORT>
[MoonPalace] 2024/07/29 17:00:29 MoonPalace Starts {'=>'} change base_url to "http://127.0.0.1:9988/v1"
[MoonPalace] 2024/07/29 21:30:53 POST   /v1/chat/completions 200 OK
[MoonPalace] 2024/07/29 21:30:53   - Request Headers: 
[MoonPalace] 2024/07/29 21:30:53     - Content-Type:   application/json
[MoonPalace] 2024/07/29 21:30:53   - Response Headers: 
[MoonPalace] 2024/07/29 21:30:53     - Content-Type:   application/json
[MoonPalace] 2024/07/29 21:30:53     - Msh-Request-Id: c34f3421-4dae-11ef-b237-9620e33511ee
[MoonPalace] 2024/07/29 21:30:53     - Server-Timing:  7134
[MoonPalace] 2024/07/29 21:30:53     - Msh-Uid:        cn0psmmcp7fclnphkcpg
[MoonPalace] 2024/07/29 21:30:53     - Msh-Gid:        enterprise-tier-5
[MoonPalace] 2024/07/29 21:30:53   - Response: 
[MoonPalace] 2024/07/29 21:30:53     - id:                cmpl-12be8428ebe74a9e8466a37bee7a9b11
[MoonPalace] 2024/07/29 21:30:53     - prompt_tokens:     1449
[MoonPalace] 2024/07/29 21:30:53     - completion_tokens: 158
[MoonPalace] 2024/07/29 21:30:53     - total_tokens:      1607
[MoonPalace] 2024/07/29 21:30:53   New Row Inserted: last_insert_id=15

MoonPalace 会以日志的形式将请求的细节在命令行中输出（假如你想将日志的内容持久化存储，你可以将 stderr 重定向到文件中）。

注：在日志中，Response Headers 中的 Msh-Request-Id 字段的值对应下文中检索请求、导出请求中的 --requestid 参数的值，Response 中的 id 对应 --chatcmpl 参数的值，last_insert_id 对应 --id 参数的值。

MoonPalace 可以检测当前 Kimi 大模型输出的内容是否被截断、或内容不完整（这一功能默认被启用）。当 MoonPalace 检测到输出的内容被截断或不完整时，会在日志中输出：

[MoonPalace] 2024/08/05 19:06:19   it seems that your max_tokens value is too small, please set a larger value

如果当前使用的是非流式输出模式（stream=False），MoonPalace 会给出建议的 max_tokens 值。

MoonPalace 提供了对 Kimi 大模型重复内容输出的检测功能。重复内容输出指的是：**Kimi 大模型会重复不断地输出某一特定字词、句子以及空白字符，并且在达到 max_tokens 限制前不会停下来。**在使用 moonshot-v1-128k 等费用较高的模型时，这种重复输出会导致额外的 Tokens 费用消耗，因此 MoonPalace 提供了 --detect-repeat 选项以启用重复内容输出检测，如下所示：

$ moonpalace start --port <PORT> --detect-repeat --repeat-threshold 0.3 --repeat-min-length 20

启用 --detect-repeat 选项后，MoonPalace 会在检测到 Kimi 大模型的重复内容输出行为时，中断 Kimi 大模型输出，并在日志中输出：

[MoonPalace] 2024/08/05 18:20:37   it appears that there is an issue with content repeating in the current response

注：启用 --detect-repeat 后，仅在流式输出（stream=True）的场合，MoonPalace 会中断 Kimi 大模型的输出，非流式输出场合不适用。

你可以使用 --repeat-threshold/--repeat-min-length 参数来调整 MoonPalace 的阻断行为：

    --repeat-threshold 参数用于设置 MoonPalace 对重复内容的容忍度，越高的 threshold 表示容忍度越低，重复内容将更快被阻断，0 <= threshold <= 1
    --repeat-min-length 参数用于设置 MoonPalace 检测重复内容输出的起始字符数量，例如：--repeat-min-length=100 表示当输出的 utf-8 字符数超过 100 时开启重复检测，输出字符数小于 100 时不开启重复内容输出检测

MoonPalace 提供了 --force-stream 的选项来强制让所有的 /v1/chat/completions 请求都使用流式输出模式：

$ moonpalace start --port <PORT> --force-stream

MoonPalace 会将请求参数中的 stream 字段设置为 True，并在获得响应时，自动根据调用方是否设置了 stream 来决定响应的格式：

    如果调用方已经设置 stream=True，则按照流式输出的格式返回，MoonPalace 不对响应做特殊处理；
    如果调用方没有设置 stream 的值，或设置了 stream=False，MoonPalace 会在接收完所有流式数据块后，将数据块拼接成完整的 completion 结构返回给调用方；

对于调用方（开发者）而言，启用 --force-stream 选项不会你获得的 Kimi API 响应内容，你仍然可以使用原先的代码逻辑来调试和运行你的程序，换句话说：开启 --force-stream 选项不会改变和破坏任何事物，你可以放心地开启这个选项。

为什么要提供这样的选项？

    我们初步推测常见的网络连接错误、超时等问题（Connection Error/Timeout）出现的原因是，在使用非流式模式进行请求的场合（stream=False），由于各中间层的网关或代理服务器对 read_header_timeout 或 read_timeout 进行了设置，导致当 Kimi API 服务端还在组装响应时，中间层的网关或代理服务器就断开了连接（由于没有收到响应，甚至是响应的 Header），产生 Connection Error/Timeout。

    我们尝试给 MoonPalace 添加了 --force-stream 参数，通过 moonpalace start --force-stream 启动时，MoonPalace 会将所有非流式请求（stream=False 或未设置 stream）转换为流式请求，并在接收完所有数据块后，组装成完整的 completion 响应结构返回给调用方。

    对于调用方而言，仍然可以使用原先的方式使用非流式 API，但经过 MoonPalace 的转换，能一定程度上减少 Connection Error/Timeout 的情况，因为此时 MoonPalace 已经与 Kimi API 服务端建立连接，并开始接收流式数据块。

在 MoonPalace 启动后，所有经过 MoonPalace 中转的请求都将被记录在一个 sqlite 数据库中，数据库所在的位置是 $HOME/.moonpalace/moonpalace.sqlite。你可以直接连接 MoonPalace 数据库以查询请求的具体内容，也可以通过 MoonPalace 命令行工具来查询请求：

$ moonpalace list
+----+--------+-------------------------------------------+--------------------------------------+---------------+---------------------+
| id | status | chatcmpl                                  | request_id                           | server_timing | requested_at        |
+----+--------+-------------------------------------------+--------------------------------------+---------------+---------------------+
| 15 | 200    | cmpl-12be8428ebe74a9e8466a37bee7a9b11     | c34f3421-4dae-11ef-b237-9620e33511ee | 7134          | 2024-07-29 21:30:53 |
| 14 | 200    | cmpl-1bf43a688a2b48eda80042583ff6fe7f     | c13280e0-4dae-11ef-9c01-debcfc72949d | 3479          | 2024-07-29 21:30:46 |
| 13 | 200    | chatcmpl-2e1aa823e2c94ebdad66450a0e6df088 | c07c118e-4dae-11ef-b423-62db244b9277 | 1033          | 2024-07-29 21:30:43 |
| 12 | 200    | cmpl-e7f984b5f80149c3adae46096a6f15c2     | 50d5686c-4d98-11ef-ba65-3613954e2587 | 774           | 2024-07-29 18:50:06 |
| 11 | 200    | chatcmpl-08f7d482b8434a869b001821cf0ee0d9 | 4c20f0a4-4d98-11ef-999a-928b67d58fa8 | 593           | 2024-07-29 18:49:58 |
| 10 | 200    | chatcmpl-6f3cf14db8e044c6bfd19689f6f66eb4 | 49f30295-4d98-11ef-95d0-7a2774525b85 | 738           | 2024-07-29 18:49:55 |
| 9  | 200    | cmpl-2a70a8c9c40e4bcc9564a5296a520431     | 7bd58976-4d8a-11ef-999a-928b67d58fa8 | 40488         | 2024-07-29 17:11:45 |
| 8  | 200    | chatcmpl-59887f868fc247a9a8da13cfbb15d04f | ceb375ea-4d7d-11ef-bd64-3aeb95b9dfac | 867           | 2024-07-29 15:40:21 |
| 7  | 200    | cmpl-36e5e21b1f544a80bf9ce3f8fc1fce57     | cd7f48d6-4d7d-11ef-999a-928b67d58fa8 | 794           | 2024-07-29 15:40:19 |
| 6  | 200    | cmpl-737d27673327465fb4827e3797abb1b3     | cc6613ac-4d7d-11ef-95d0-7a2774525b85 | 670           | 2024-07-29 15:40:17 |
+----+--------+-------------------------------------------+--------------------------------------+---------------+---------------------+

使用 list 命令将查询最近产生的请求内容，默认展示的字段是便于检索的 id/chatcmpl/request_id 以及用于查看请求状态的 status/server_timing/requested_at 信息。如果你想查看某个具体的请求，你可以使用 inspect 命令来检索对应的请求：

# 以下三条命令会检索出相同的请求信息
$ moonpalace inspect --id 13
$ moonpalace inspect --chatcmpl chatcmpl-2e1aa823e2c94ebdad66450a0e6df088
$ moonpalace inspect --requestid c07c118e-4dae-11ef-b423-62db244b9277
+--------------------------------------------------------------+
| metadata                                                     |
+--------------------------------------------------------------+
| {                                                            |
|     "chatcmpl": "chatcmpl-2e1aa823e2c94ebdad66450a0e6df088", |
|     "content_type": "application/json",                      |
|     "group_id": "enterprise-tier-5",                         |
|     "moonpalace_id": "13",                                   |
|     "request_id": "c07c118e-4dae-11ef-b423-62db244b9277",    |
|     "requested_at": "2024-07-29 21:30:43",                   |
|     "server_timing": "1033",                                 |
|     "status": "200 OK",                                      |
|     "user_id": "cn0psmmcp7fclnphkcpg"                        |
| }                                                            |
+--------------------------------------------------------------+

在默认情况下，inspect 命令不会打印出请求和响应的 body 信息，如果你想打印出 body，你可以使用如下的命令：

$ moonpalace inspect --chatcmpl chatcmpl-2e1aa823e2c94ebdad66450a0e6df088 --print request_body,response_body
# 由于 body 信息过于冗长，这里不再完整展示 body 详细内容
+--------------------------------------------------+--------------------------------------------------+
| request_body                                     | response_body                                    |
+--------------------------------------------------+--------------------------------------------------+
| ...                                              | ...                                              |
+--------------------------------------------------+--------------------------------------------------+

当你认为某个请求不符合预期，或是想向 Moonshot AI 报告某个请求时（无论是 Good Case 还是 Bad Case，我们都欢迎），你可以使用 export 命令导出特定的请求：

# id/chatcmpl/requestid 选项只需要任选其一即可检索出对应的请求
$ moonpalace export \
    --id 13 \
    --chatcmpl chatcmpl-2e1aa823e2c94ebdad66450a0e6df088 \
    --requestid c07c118e-4dae-11ef-b423-62db244b9277 \
    --good/--bad \
    --tag "code" --tag "python" \
    --directory $HOME/Downloads/

其中，id/chatcmpl/requestid 用法与 inspect 命令相同，用于检索一个特定的请求，--good/--bad 用于标记当前请求是 Good Case 或是 Bad Case，--tag 用于为当前请求打上对应的标签，例如在上述例子中，我们假设当前请求内容与编程语言 Python 相关，因此为其添加两个 tag，分别是 code 和 python，--directory 用于指定导出文件存储的目录的路径。

成功导出的文件内容为：

$ cat $HOME/Downloads/chatcmpl-2e1aa823e2c94ebdad66450a0e6df088.json
{
    "metadata":
    {
        "chatcmpl": "chatcmpl-2e1aa823e2c94ebdad66450a0e6df088",
        "content_type": "application/json",
        "group_id": "enterprise-tier-5",
        "moonpalace_id": "13",
        "request_id": "c07c118e-4dae-11ef-b423-62db244b9277",
        "requested_at": "2024-07-29 21:30:43",
        "server_timing": "1033",
        "status": "200 OK",
        "user_id": "cn0psmmcp7fclnphkcpg"
    },
    "request":
    {
        "url": "https://api.moonshot.cn/v1/chat/completions",
        "header": "Accept: application/json\r\nAccept-Encoding: gzip\r\nConnection: keep-alive\r\nContent-Length: 2450\r\nContent-Type: application/json\r\nUser-Agent: OpenAI/Python 1.36.1\r\nX-Stainless-Arch: arm64\r\nX-Stainless-Async: false\r\nX-Stainless-Lang: python\r\nX-Stainless-Os: MacOS\r\nX-Stainless-Package-Version: 1.36.1\r\nX-Stainless-Runtime: CPython\r\nX-Stainless-Runtime-Version: 3.11.6\r\n",
        "body":
        {}
    },
    "response":
    {
        "status": "200 OK",
        "header": "Content-Encoding: gzip\r\nContent-Type: application/json; charset=utf-8\r\nDate: Mon, 29 Jul 2024 13:30:43 GMT\r\nMsh-Cache: updated\r\nMsh-Gid: enterprise-tier-5\r\nMsh-Request-Id: c07c118e-4dae-11ef-b423-62db244b9277\r\nMsh-Trace-Mode: on\r\nMsh-Uid: cn0psmmcp7fclnphkcpg\r\nServer: nginx\r\nServer-Timing: inner; dur=1033\r\nStrict-Transport-Security: max-age=15724800; includeSubDomains\r\nVary: Accept-Encoding\r\nVary: Origin\r\n",
        "body":
        {}
    },
    "category": "goodcase",
    "tags":
    [
        "code",
        "python"
    ]
}

我们推荐开发者使用 Github Issues

提交 Good Case 或 Bad Case，但如果你不想公开你的请求信息，你也可以通过企业微信、电子邮件等方式将 Case 投递给我们。

你可以将导出的文件投递至以下邮箱：

<EMAIL>
Last updated on 2025年7月29日
从 OpenAI 迁移到 Kimi API
开始使用 Kimi API

MoonPalace - Moonshot AI 月之暗面 Kimi API 调试工具 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗
文档
入门指南
开始使用 Kimi API
开始使用 Kimi API

Kimi API 提供了与 Kimi 大模型交互的能力，以下是一个简单示例代码：

from openai import OpenAI
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model = "kimi-k2-0711-preview",
    messages = [
        {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
        {"role": "user", "content": "你好，我叫李雷，1+1等于多少？"}
    ],
    temperature = 0.6,
)
 
# 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）
print(completion.choices[0].message.content)

为了成功运行上述代码，你可能需要准备：

    Python 环境 或 Node.js 环境，我们推荐使用 Python 3.8 及以上版本的 Python 解释器；
    OpenAI SDK，我们的 API 完全兼容 OpenAI 的 API 格式，因此你可以直接使用 Python 或 Node.js OpenAI SDK 进行调用，你可以通过如下方式来安装 OpenAI SDK：

    pip install --upgrade 'openai>=1.0' #Python
    npm install openai@latest #Node.js

    API Key，你需要从 Kimi 开放平台中申请一个 API Key，将其传入 OpenAi Client 以便于我们能正确识别你的身份；

如果您成功运行上述代码，且没有任何报错，那么您将看到类似如下的内容输出：

你好，李雷！1+1 等于 2。这是一个基本的数学加法问题。如果你有其他问题或需要帮助，请随时告诉我。

注：由于 Kimi 大模型的不确定性，实际的回复内容可能并不与上述内容完全一致。
Last updated on 2025年7月29日
使用 API 调试工具
使用 Kimi API 进行多轮对话

开始使用 Kimi API - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗
文档
入门指南
使用 Kimi API 进行多轮对话
使用 Kimi API 进行多轮对话

Kimi API 与 Kimi 智能助手不同，API 本身不具有记忆功能，它是无状态的，这意味着，当你多次请求 API 时，Kimi 大模型并不知道你前一次请求的内容，也不会记忆任何请求的上下文信息。例如，你在前一次请求中告诉 Kimi 大模型你今年 27 岁，在下一次请求中，Kimi 大模型并不会记住你 27 岁这件事。

因此，我们需要手动维护每次请求的上下文，即 Context，把上一次请求过的内容手动加入到下一次请求中，让 Kimi 大模型能正确看到此前我们都聊了什么。我们将改造上一章节中使用的示例，来展示如何通过维护 messages 列表让 Kimi 大模型拥有记忆，并实现多轮对话功能。

注：我们将实现多轮对话的要点以注释的形式添加到代码中。

from openai import OpenAI
 
client = OpenAI(
    api_key = "MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url = "https://api.moonshot.cn/v1",
)
 
# 我们定义一个全局变量 messages，用于记录我们和 Kimi 大模型产生的历史对话消息
# 在 messages 中，既包含我们向 Kimi 大模型提出的问题（role=user），也包括 Kimi 大模型给我们的回复（role=assistant）
# 当然，也包括初始的 System Prompt（role=system）
# messages 中的消息按时间顺序从小到大排列
messages = [
	{"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
]
 
def chat(input: str) -> str:
	"""
	chat 函数支持多轮对话，每次调用 chat 函数与 Kimi 大模型对话时，Kimi 大模型都会”看到“此前已经
	产生的历史对话消息，换句话说，Kimi 大模型拥有了记忆。
	"""
 
  global messages
 
	# 我们将用户最新的问题构造成一个 message（role=user），并添加到 messages 的尾部
	messages.append({
		"role": "user",
		"content": input,	
	})
 
	# 携带 messages 与 Kimi 大模型对话
	completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=messages,
        temperature=0.6,
    )
 
	# 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）
    assistant_message = completion.choices[0].message
 
    # 为了让 Kimi 大模型拥有完整的记忆，我们必须将 Kimi 大模型返回给我们的消息也添加到 messages 中
    messages.append(assistant_message)
 
    return assistant_message.content
 
print(chat("你好，我今年 27 岁。"))
print(chat("你知道我今年几岁吗？")) # 在这里，Kimi 大模型根据此前的上下文信息，将会知道你今年的年龄是 27 岁

我们回顾一下上述代码中的要点：

    Kimi API 本身没有上下文记忆功能，我们需要通过 API 中的 messages 参数，手动将”之前聊了什么“告知 Kimi 大模型；
    在 messages 中，既要存储我们向 Kimi 大模型提出的问题消息（role=user），也要存储 Kimi 大模型给我们的回复消息（role=assistant）；

需要注意的是，在上述代码中，随着 chat 调用次数的不断增多，messages 列表的长度也在不断增加，这意味着每次请求所消耗的 Tokens 数量也在不断增加，并且最终会在某个时间点，messages 中的消息所占用的 Tokens 超过了 Kimi 大模型支持的上下文窗口大小。我们推荐你使用某种策略来保持 messages 列表的消息数量在一个可控的范围内，例如，每次只保留最新的 20 条消息作为本次请求的上下文。

我们给出一个示例，以便于你能顺利理解如何控制上下文长度，请注意关注 make_messages 函数是如何运作的：

from openai import OpenAI
 
client = OpenAI(
    api_key = "MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url = "https://api.moonshot.cn/v1",
)
 
# 我们将 System Messages 单独放置在一个列表中，这是因为每次请求都应该携带 System Messages
system_messages = [
	{"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
]
 
# 我们定义一个全局变量 messages，用于记录我们和 Kimi 大模型产生的历史对话消息
# 在 messages 中，既包含我们向 Kimi 大模型提出的问题（role=user），也包括 Kimi 大模型给我们的回复（role=assistant）
# messages 中的消息按时间顺序从小到大排列
messages = []
 
 
def make_messages(input: str, n: int = 20) -> list[dict]:
	"""
	使用 make_messaegs 控制每次请求的消息数量，使其保持在一个合理的范围内，例如默认值是 20。在构建消息列表
	的过程中，我们会先添加 System Prompt，这是因为无论如何对消息进行截断，System Prompt 都是必不可少
	的内容，再获取 messages —— 即历史记录中，最新的 n 条消息作为请求使用的消息，在大部分场景中，这样
	能保证请求的消息所占用的 Tokens 数量不超过模型上下文窗口。
	"""
	# 首先，我们将用户最新的问题构造成一个 message（role=user），并添加到 messages 的尾部
	messages.append({
		"role": "user",
		"content": input,	
	})
 
	# new_messages 是我们下一次请求使用的消息列表，现在让我们来构建它
	new_messages = []
 
	# 每次请求都需要携带 System Messages，因此我们需要先把 system_messages 添加到消息列表中；
	# 注意，即使对消息进行截断，也应该注意保证 System Messages 仍然在 messages 列表中。
	new_messages.extend(system_messages)
 
	# 在这里，当历史消息超过 n 条时，我们仅保留最新的 n 条消息
	if len(messages) > n:
		messages = messages[-n:]
 
	new_messages.extend(messages)
	return new_messages
 
 
def chat(input: str) -> str:
	"""
	chat 函数支持多轮对话，每次调用 chat 函数与 Kimi 大模型对话时，Kimi 大模型都会”看到“此前已经
	产生的历史对话消息，换句话说，Kimi 大模型拥有了记忆。
	"""
 
	# 携带 messages 与 Kimi 大模型对话
	completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=make_messages(input),
        temperature=0.6,
    )
 
	# 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）
    assistant_message = completion.choices[0].message
 
    # 为了让 Kimi 大模型拥有完整的记忆，我们必须将 Kimi 大模型返回给我们的消息也添加到 messages 中
    messages.append(assistant_message)
 
    return assistant_message.content
 
print(chat("你好，我今年 27 岁。"))
print(chat("你知道我今年几岁吗？")) # 在这里，Kimi 大模型根据此前的上下文信息，将会知道你今年的年龄是 27 岁

请注意，上述的代码示例仅考虑了最简单的调用场景，在实际的业务代码逻辑中，你可能需要考虑更多的场景和边界，例如：

    并发场景下可能需要额外的读写锁；
    针对多用户场景，需要为每个用户单独维护 messages 列表；
    你可能需要对 messages 列表进行持久化；
    你可能仍然需要更精确的方式计算 messages 列表中需要保留多少条消息；
    你可能想对被遗弃的消息做一次总结，并生成一条新的消息加入到 messages 列表中；
    ……

Last updated on 2025年7月29日
开始使用 Kimi API
使用 Vision 视觉模型
已复制链接

使用 Kimi API 进行多轮对话 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    Tokens 计算及费用
    功能说明及限制

文档
入门指南
使用 Vision 视觉模型
使用 Kimi 视觉模型（Vision）

Kimi 视觉模型（包括 moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview 等）能够理解图片内容，包括图片文字、图片颜色和物体形状等内容。我们通过以下代码来向 Kimi 提问有关图片的内容：

import os
import base64
 
from openai import OpenAI
 
client = OpenAI(
    api_key=os.environ.get("MOONSHOT_API_KEY"),
    base_url="https://api.moonshot.cn/v1",
)
 
# 在这里，你需要将 kimi.png 文件替换为你想让 Kimi 识别的图片的地址
image_path = "kimi.png"
 
with open(image_path, "rb") as f:
    image_data = f.read()
 
# 我们使用标准库 base64.b64encode 函数将图片编码成 base64 格式的 image_url
image_url = f"data:image/{os.path.splitext(image_path)[1]};base64,{base64.b64encode(image_data).decode('utf-8')}"
 
 
completion = client.chat.completions.create(
    model="moonshot-v1-8k-vision-preview",
    messages=[
        {"role": "system", "content": "你是 Kimi。"},
        {
            "role": "user",
            # 注意这里，content 由原来的 str 类型变更为一个 list，这个 list 中包含多个部分的内容，图片（image_url）是一个部分（part），
            # 文字（text）是一个部分（part）
            "content": [
                {
                    "type": "image_url", # <-- 使用 image_url 类型来上传图片，内容为使用 base64 编码过的图片内容
                    "image_url": {
                        "url": image_url,
                    },
                },
                {
                    "type": "text",
                    "text": "请描述图片的内容。", # <-- 使用 text 类型来提供文字指令，例如“描述图片内容”
                },
            ],
        },
    ],
)
 
print(completion.choices[0].message.content)

注意，在使用 Vision 模型时，message.content 字段的类型由 str 变更为 List[Dict]（即 JSON 数组）。额外的，请不要将 JSON 数组序列化后以 str 的格式放入 message.content 中，这样会导致 Kimi 无法正确识别图片类型，并可能引发 Your request exceeded model token limit 错误。

✅ 正确的格式：

{
    "model": "moonshot-v1-8k-vision-preview",
    "messages":
    [
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"
        },
        {
            "role": "user",
            "content":
            [
                {
                    "type": "image_url",
                    "image_url":
                    {
                        "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABhCAYAAAApxKSdAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUUSURBVHgB7Z29bhtHFIWPHQN2J7lKqnhYpYvpIukCbJEAKQJEegLReYFIT0DrCSI9QEDqCSIDaQIEIOukiJwyza5SJWlId3FFz+HuGmuSSw6p+dlZ3g84luhdUeI9M3fmziyXgBCUe/DHYY0Wj/tgWmjV42zFcWe4MIBBPNJ6qqW0uvAbXFvQgKzQK62bQhkaCIPc10q1Zi3XH1o/IG9cwUm0RogrgDY1KmLgHYX9DvyiBvDYI77XmiD+oLlQHw7hIDoCMBOt1U9w0BsU9mOAtaUUFk3oQoIfzAQFCf5dNMEdTFCQ4NtQih1NSIGgf3ibxOJt5UrAB1gNK72vIdjiI61HWr+YnNxDXK0rJiULsV65GJeiIescLSTTeobKSutiCuojX8kU3MBx4I3WeNVBBRl4fWiCyoB8v2JAAkk9PmDwT8sH1TEghRjgC27scCx41wO43KAg+ILxTvhNaUACwTc04Z0B30LwzTzm5Rjw3sgseIG1wGMawMBPIOQcqvzrNIMHOg9Q5KK953O90/rFC+BhJRH8PQZ+fu7SjC7HAIV95yu99vjlxfvBJx8nwHd6IfNJAkccOjHg6OgIs9lsra6vr2GTNE03/k7q8HAhyJ/2gM9O65/4kT7/mwEcoZwYsPQiV3BwcABb9Ho9KKU2njccDjGdLlxx+InBBPBAAR86ydRPaIC9SASi3+8bnXd+fr78nw8NJ39uDJjXAVFPP7dp/VmWLR9g6w6Huo/IOTk5MTpvZesn/93AiP/dXCwd9SyILT9Jko3n1bZ+8s8rGPGvoVHbEXcPMM39V1dX9Qd/19PPNxta959D4HUGF0RrAFs/8/8mxuPxXLUwtfx2WX+cxdivZ3DFA0SKldZPuPTAKrikbOlMOX+9zFu/Q2iAQoSY5H7mfeb/tXCT8MdneU9wNNCuQUXZA0ynnrUznyqOcrspUY4BJunHqPU3gOgMsNr6G0B0BpgUXrG0fhKVAaaF1/HxMWIhKgNMcj9Tz82Nk6rVGdav/tJ5eraJ0Wi01XPq1r/xOS8uLkJc6XYnRTMNXdf62eIvLy+jyftVghnQ7Xahe8FW59fBTRYOzosDNI1hJdz0lBQkBflkMBjMU5iL13pXRb8fYAJrB/a2db0oFHthAOEUliaYFHE+aaUBdZsvvFhApyM0idYZwOCvW4JmIWdSzPmidQaYrAGZ7iX4oFUGnJ2dGdUCTRqMozeANQCLsE6nA10JG/0Mx4KmDMbBCjEWR2yxu8LAM98vXelmCA2ovVLCI8EMYODWbpbvCXtTBzQVMSAwYkBgxIDAtNKAXWdGIRADAiMpKDA0IIMQikx6QGDEgMCIAYGRMSAsMgaEhgbcQgjFa+kBYZnIGBCWWzEgLPNBOJ6Fk/aR8Y5ZCvktKwX/PJZ7xoVjfs+4chYU11tK2sE85qUBLyH4Zh5z6QHhGPOf6r2j+TEbcgdFP2RaHX5TrYQlDflj5RXE5Q1cG/lWnhYpReUGKdUewGnRmhvnCJbgmxey8sHiZ8iwF3AsUBBckKHI/SWLq6HsBc8huML4DiK80D6WnBqLzN68UFCmopheYJOVYgcU5FOVbAVfYUcUZGoaLPglCtITdg2+tZUFBTFh2+ArWEYh/7z0WIIQSiM43lt5AWAmWhLHylN4QmkNEXfAbGqEQKsHSfHLYwiSq8AnaAAKeaW3D8VbijwNW5nh3IN9FPI/jnpaPKZi2/SfFuJu4W3x9RqWL+N5C+7ruKpBAgLkAAAAAElFTkSuQmCC"
                    }
                },
                {
                    "type": "text",
                    "text": "请描述这个图片"
                }
            ]
        }
    ],
    "temperature": 0.3
}

❌ 错误的格式：

{
    "model": "moonshot-v1-8k-vision-preview",
    "messages":
    [
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"
        },
        {
            "role": "user",
            "content": "[{\"type\": \"image_url\", \"image_url\": {\"url\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABhCAYAAAApxKSdAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUUSURBVHgB7Z29bhtHFIWPHQN2J7lKqnhYpYvpIukCbJEAKQJEegLReYFIT0DrCSI9QEDqCSIDaQIEIOukiJwyza5SJWlId3FFz+HuGmuSSw6p+dlZ3g84luhdUeI9M3fmziyXgBCUe/DHYY0Wj/tgWmjV42zFcWe4MIBBPNJ6qqW0uvAbXFvQgKzQK62bQhkaCIPc10q1Zi3XH1o/IG9cwUm0RogrgDY1KmLgHYX9DvyiBvDYI77XmiD+oLlQHw7hIDoCMBOt1U9w0BsU9mOAtaUUFk3oQoIfzAQFCf5dNMEdTFCQ4NtQih1NSIGgf3ibxOJt5UrAB1gNK72vIdjiI61HWr+YnNxDXK0rJiULsV65GJeiIescLSTTeobKSutiCuojX8kU3MBx4I3WeNVBBRl4fWiCyoB8v2JAAkk9PmDwT8sH1TEghRjgC27scCx41wO43KAg+ILxTvhNaUACwTc04Z0B30LwzTzm5Rjw3sgseIG1wGMawMBPIOQcqvzrNIMHOg9Q5KK953O90/rFC+BhJRH8PQZ+fu7SjC7HAIV95yu99vjlxfvBJx8nwHd6IfNJAkccOjHg6OgIs9lsra6vr2GTNE03/k7q8HAhyJ/2gM9O65/4kT7/mwEcoZwYsPQiV3BwcABb9Ho9KKU2njccDjGdLlxx+InBBPBAAR86ydRPaIC9SASi3+8bnXd+fr78nw8NJ39uDJjXAVFPP7dp/VmWLR9g6w6Huo/IOTk5MTpvZesn/93AiP/dXCwd9SyILT9Jko3n1bZ+8s8rGPGvoVHbEXcPMM39V1dX9Qd/19PPNxta959D4HUGF0RrAFs/8/8mxuPxXLUwtfx2WX+cxdivZ3DFA0SKldZPuPTAKrikbOlMOX+9zFu/Q2iAQoSY5H7mfeb/tXCT8MdneU9wNNCuQUXZA0ynnrUznyqOcrspUY4BJunHqPU3gOgMsNr6G0B0BpgUXrG0fhKVAaaF1/HxMWIhKgNMcj9Tz82Nk6rVGdav/tJ5eraJ0Wi01XPq1r/xOS8uLkJc6XYnRTMNXdf62eIvLy+jyftVghnQ7Xahe8FW59fBTRYOzosDNI1hJdz0lBQkBflkMBjMU5iL13pXRb8fYAJrB/a2db0oFHthAOEUliaYFHE+aaUBdZsvvFhApyM0idYZwOCvW4JmIWdSzPmidQaYrAGZ7iX4oFUGnJ2dGdUCTRqMozeANQCLsE6nA10JG/0Mx4KmDMbBCjEWR2yxu8LAM98vXelmCA2ovVLCI8EMYODWbpbvCXtTBzQVMSAwYkBgxIDAtNKAXWdGIRADAiMpKDA0IIMQikx6QGDEgMCIAYGRMSAsMgaEhgbcQgjFa+kBYZnIGBCWWzEgLPNBOJ6Fk/aR8Y5ZCvktKwX/PJZ7xoVjfs+4chYU11tK2sE85qUBLyH4Zh5z6QHhGPOf6r2j+TEbcgdFP2RaHX5TrYQlDflj5RXE5Q1cG/lWnhYpReUGKdUewGnRmhvnCJbgmxey8sHiZ8iwF3AsUBBckKHI/SWLq6HsBc8huML4DiK80D6WnBqLzN68UFCmopheYJOVYgcU5FOVbAVfYUcUZGoaLPglCtITdg2+tZUFBTFh2+ArWEYh/7z0WIIQSiM43lt5AWAmWhLHylN4QmkNEXfAbGqEQKsHSfHLYwiSq8AnaAAKeaW3D8VbijwNW5nh3IN9FPI/jnpaPKZi2/SfFuJu4W3x9RqWL+N5C+7ruKpBAgLkAAAAAElFTkSuQmCC\"}}, {\"type\": \"text\", \"text\": \"请描述这个图片\"}]"
        }
    ],
    "temperature": 0.3
}

目前，每张图片消耗的 Tokens 为固定值 1024（不区分图片尺寸及图片质量）。

Vision 模型在计费方式上与 moonshot-v1 系列模型保持一致，根据模型推理的总 Tokens 计费，详情请查看：

模型推理价格说明

Vision 视觉模型支持的特性包括：

    多轮对话
    流式输出
    工具调用
    JSON Mode
    Partial Mode

以下功能暂未支持或部分支持

    联网搜索：不支持
    Context Caching：不支持创建带有图片内容的 Context Cache，但支持使用已经创建成功的 Cache 调用 Vision 模型
    URL 格式的图片：不支持，目前仅支持使用 base64 编码的图片内容

其他限制：

    图片数量：Vision 模型没有图片数量限制，但请确保请求的 Body 大小不超过 100M

Last updated on 2025年7月29日
使用 Kimi API 进行多轮对话
自动选择 Kimi 模型
已复制链接
已复制链接

使用 Kimi 视觉模型（Vision） - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    moonshot-v1-auto 模型
    手动选择合适的模型

文档
入门指南
自动选择 Kimi 模型
选择合适的 Kimi 大模型

在上一章节中，我们通过一个简单的示例讲述了如何快速使用 OpenAI SDK 调用 Kimi 大模型完成多轮对话，让我们复习一下相关内容：

from openai import OpenAI
 
client = OpenAI(
    api_key = "MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url = "https://api.moonshot.cn/v1",
)
 
# 我们定义一个全局变量 messages，用于记录我们和 Kimi 大模型产生的历史对话消息
# 在 messages 中，既包含我们向 Kimi 大模型提出的问题（role=user），也包括 Kimi 大模型给我们的回复（role=assistant）
# 当然，也包括初始的 System Prompt（role=system）
# messages 中的消息按时间顺序从小到大排列
messages = [
	{"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
]
 
def chat(input: str) -> str:
	"""
	chat 函数支持多轮对话，每次调用 chat 函数与 Kimi 大模型对话时，Kimi 大模型都会”看到“此前已经
	产生的历史对话消息，换句话说，Kimi 大模型拥有了记忆。
	"""
 
	# 我们将用户最新的问题构造成一个 message（role=user），并添加到 messages 的尾部
	messages.append({
		"role": "user",
		"content": input,	
	})
 
	# 携带 messages 与 Kimi 大模型对话
	completion = client.chat.completions.create(
        model="moonshot-v1-8k",
        messages=messages,
        temperature=0.3,
    )
 
	# 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）
    assistant_message = completion.choices[0].message
 
    # 为了让 Kimi 大模型拥有完整的记忆，我们必须将 Kimi 大模型返回给我们的消息也添加到 messages 中
    messages.append(assistant_message)
 
    return assistant_message.content
 
print(chat("你好，我今年 27 岁。"))
print(chat("你知道我今年几岁吗？")) # 在这里，Kimi 大模型根据此前的上下文信息，将会知道你今年的年龄是 27 岁

我们在上一个章节中曾经提到，对于多轮对话，当对话的轮次越来越多时，历史消息会占用越来越多的 Tokens，然而，我们的代码中使用的是固定的 moonshot-v1-8k 模型，这意味着当历史消息膨胀到超过 8192 个 Tokens 时，继续调用 chat 函数会获得一个 Your request exceeded model token limit 错误。此时，如果你想继续刚才的上下文接着与 Kimi 大模型对话，那你需要切换一个更大上下文的模型，例如 moonshot-v1-32k。

在某些场合，你也许并不知道你的用户下一次输入会占用多少 Tokens，因此你也不知道应该如何选择一个合适的 Kimi 大模型（虽然直接选择 moonshot-v1-128k 总是不会错，但这会使你的账户余额消耗得非常快）。你可能会想，是否存在一种手段或者方式，能根据消息占用的 Tokens 数量自动选择恰当的模型？

moonshot-v1-auto 可以根据当前上下文占用的 Tokens 数量来选择合适的模型，可供选择的模型包括：

    moonshot-v1-8k
    moonshot-v1-32k
    moonshot-v1-128k

moonshot-v1-auto 可以被认为是一个模型路由器，他根据当前上下文占用的 Tokens 来决定选择哪个具体的模型，从效果上说，moonshot-v1-auto 与上述模型并无差别。

moonshot-v1-auto 的计费方式根据最终选择的模型决定，它的路由规则是（以代码举例）：

def select_model(prompt_tokens: int, max_tokens: int) -> str:
	"""
	prompt_tokens: 指当前请求中上下文（messages + tools）占用的 Tokens 数量
	max_tokens:    指当前请求传递的 max_tokens 参数值，未设置该值时，默认指为 1024
	"""
	total_tokens = prompt_tokens + max_tokens
    if total_tokens <= 8 * 1024:
        return "moonshot-v1-8k"
    elif total_tokens <= 32 * 1024:
        return "moonshot-v1-32k"
    else:
        return "moonshot-v1-128k"

关于计费的详细信息，请参考模型推理。

moonshot-v1-auto 的使用方式与普通模型并无差别，在上述代码中，你只需要做出如下改动：

from openai import OpenAI
 
client = OpenAI(
    api_key = "MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url = "https://api.moonshot.cn/v1",
)
 
# 我们定义一个全局变量 messages，用于记录我们和 Kimi 大模型产生的历史对话消息
# 在 messages 中，既包含我们向 Kimi 大模型提出的问题（role=user），也包括 Kimi 大模型给我们的回复（role=assistant）
# 当然，也包括初始的 System Prompt（role=system）
# messages 中的消息按时间顺序从小到大排列
messages = [
	{"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
]
 
def chat(input: str) -> str:
	"""
	chat 函数支持多轮对话，每次调用 chat 函数与 Kimi 大模型对话时，Kimi 大模型都会”看到“此前已经
	产生的历史对话消息，换句话说，Kimi 大模型拥有了记忆。
	"""
 
	# 我们将用户最新的问题构造成一个 message（role=user），并添加到 messages 的尾部
	messages.append({
		"role": "user",
		"content": input,	
	})
 
	# 携带 messages 与 Kimi 大模型对话
	completion = client.chat.completions.create(
        model="moonshot-v1-auto",  # <-- 注意这里，将原先的 moonshot-v1-8k 修改为 moonshot-v1-auto
        messages=messages,
        temperature=0.3,
    )
 
	# 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）
    assistant_message = completion.choices[0].message
 
    # 为了让 Kimi 大模型拥有完整的记忆，我们必须将 Kimi 大模型返回给我们的消息也添加到 messages 中
    messages.append(assistant_message)
 
    return assistant_message.content
 
print(chat("你好，我今年 27 岁。"))
print(chat("你知道我今年几岁吗？")) # 在这里，Kimi 大模型根据此前的上下文信息，将会知道你今年的年龄是 27 岁

注意第 30 行的改动，将 moonshot-v1-8k 修改为 moonshot-v1-auto 即可。

如果你想手动计算 Tokens 数量并选择合适的模型，可以参考以下代码：

import os
import httpx
from openai import OpenAI
 
client = OpenAI(
    api_key=os.environ['MOONSHOT_API_KEY'],
    base_url="https://api.moonshot.cn/v1",
)
 
 
def estimate_token_count(input_messages) -> int:
    """
    在这里实现你的 Tokens 计算逻辑，或是直接调用我们的 Tokens 计算接口计算 Tokens
 
    https://api.moonshot.cn/v1/tokenizers/estimate-token-count
    """
    header = {
        "Authorization": f"Bearer {os.environ['MOONSHOT_API_KEY']}",
    }
    data = {
        "model": "moonshot-v1-128k",
        "messages": input_messages,
    }
    r = httpx.post("https://api.moonshot.cn/v1/tokenizers/estimate-token-count", headers=header, json=data)
    r.raise_for_status()
    return r.json()["data"]["total_tokens"]
 
 
def select_model(input_messages, max_tokens=1024) -> str:
    """
    select_model 根据输入的上下文消息 input_messages，以及预期的 max_tokens 值，
    选择一个大小合适的模型。
 
    select_model 内部会调用 estimate_token_count 函数计算 input_messages 所占用
    的 tokens 数量，加上 max_tokens 的值作为 total_tokens，并根据 total_tokens
    所处的区间选择恰当的模型。
    """
    prompt_tokens = estimate_token_count(input_messages)
    total_tokens = prompt_tokens + max_tokens
    if total_tokens <= 8 * 1024:
        return "moonshot-v1-8k"
    elif total_tokens <= 32 * 1024:
        return "moonshot-v1-32k"
    elif total_tokens <= 128 * 1024:
        return "moonshot-v1-128k"
    else:
        raise Exception("too many tokens 😢")
 
 
messages = [
    {"role": "system", "content": "你是 Kimi"},
    {"role": "user", "content": "你好，请给我讲一个童话故事。"},
]
 
max_tokens = 2048
model = select_model(messages, max_tokens)
 
completion = client.chat.completions.create(
    model=model,
    messages=messages,
    max_tokens=max_tokens,
    temperature=0.3,
)
 
print("model:", model)
print("max_tokens:", max_tokens)
print("completion:", completion.choices[0].message.content)

Last updated on 2025年7月29日
使用 Vision 视觉模型
自动断线重连

选择合适的 Kimi 大模型 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗
文档
入门指南
自动断线重连
自动断线重连

因为并发限制、复杂的网络环境等情况，一些时候我们的连接可能因为一些预期外的状况而中断，通常这种偶发的中断并不会持续很久，我们希望在这种情况下业务依然可以稳定运行，使用简单的代码即可实现断线重连的需求。

from openai import OpenAI
import time
 
client = OpenAI(
    api_key = "$MOONSHOT_API_KEY",
    base_url = "https://api.moonshot.cn/v1",
)
 
def chat_once(msgs):
    response = client.chat.completions.create(
        model = "kimi-k2-0711-preview",
        messages = msgs,
        temperature = 0.6,
    )
    return response.choices[0].message.content
 
def chat(input: str, max_attempts: int = 100) -> str:
    messages = [
	    {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
    ]
 
	# 我们将用户最新的问题构造成一个 message（role=user），并添加到 messages 的尾部
    messages.append({
		"role": "user",
		"content": input,	
	})
    st_time = time.time()  
    for i in range(max_attempts):
        print(f"Attempts: {i+1}/{max_attempts}")
        try:
            response = chat_once(messages)
            ed_time = time.time()
            print("Query Succuess!")
            print(f"Query Time: {ed_time-st_time}")
            return response
        except Exception as e:
            print(e)
            time.sleep(1)
            continue
 
    print("Query Failed.")
    return
 
print(chat("你好，请给我讲一个童话故事。"))

上面的代码实现了一个简单的断线重连功能，最多重复 100 次，每次连接之间等待 1s，你也可以根据具体的需求更改这些数值以及满足重试的条件。
Last updated on 2025年7月29日
自动选择 Kimi 模型
使用 Stream 流式输出

自动断线重连 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    如何使用流式输出
    使用流式输出时的常见问题
    接口细节
    Tokens 计算
    如何终止输出
    在不使用 SDK 的场合下如何处理流式输出
    n>1 时如何处理

文档
入门指南
使用 Stream 流式输出
使用 Kimi API 的流式输出功能 —— Streaming

Kimi 大模型在收到用户提出的问题后，会先进行推理、再逐个 Token 生成回答，在我们前两个章节的例子中，我们都选择等待 Kimi 大模型将所有 Tokens 生成完毕后，再打印（print）Kimi 大模型回复的内容，这通常要花费数秒的时间。如果你的问题足够复杂，且 Kimi 大模型生成的回复长度足够长，完整等待模型生成结果的时间可能会被拉长到 10 秒甚至 20 秒，这会极大降低用户的使用体验。为了改善这种情况，并及时给予用户反馈，我们提供了流式输出的能力，即 Streaming，我们将讲解 Streaming 的原理，并结合实际的代码来说明：

    如何使用流式输出；
    使用流式输出时的常见问题；
    在不使用 Python SDK 的场合下如何处理流式输出；

流式输出（Streaming），一言以蔽之，就是每当 Kimi 大模型生成了一定数量的 Tokens 时（通常情况下，这个数量是 1 Token），立刻将这些 Tokens 传输给客户端，而不再是等待所有 Tokens 生成完毕后再传输给客户端。当你与 Kimi 智能助手

进行对话时，Kimi 智能助手的回复是按字符逐个“跳”出来的，这即是流式输出的表现之一，流式输出能让用户第一时间看到 Kimi 大模型输出的第一个 Token，减少用户的等待时间。

你可以通过这样的方式（stream=True）来使用流式输出，并获得流式输出的响应：

from openai import OpenAI
 
client = OpenAI(
    api_key = "MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url = "https://api.moonshot.cn/v1",
)
 
stream = client.chat.completions.create(
    model = "kimi-k2-0711-preview",
    messages = [
        {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
        {"role": "user", "content": "你好，我叫李雷，1+1等于多少？"}
    ],
    temperature = 0.6,
    stream=True, # <-- 注意这里，我们通过设置 stream=True 开启流式输出模式
)
 
# 当启用流式输出模式（stream=True），SDK 返回的内容也发生了变化，我们不再直接访问返回值中的 choice
# 而是通过 for 循环逐个访问返回值中每个单独的块（chunk）
 
for chunk in stream:
	# 在这里，每个 chunk 的结构都与之前的 completion 相似，但 message 字段被替换成了 delta 字段
	delta = chunk.choices[0].delta # <-- message 字段被替换成了 delta 字段
 
	if delta.content:
		# 我们在打印内容时，由于是流式输出，为了保证句子的连贯性，我们不人为地添加
		# 换行符，因此通过设置 end="" 来取消 print 自带的换行符。
		print(delta.content, end="")

当您成功运行上述代码，并了解了流式输出的基本原理后，现在让我们向你讲述一些流式输出的细节和常见问题，以便于你更好的实现自己的业务逻辑。

当启用流式输出模式（stream=True）时，Kimi 大模型不再返回一个 JSON 格式（Content-Type: application/json）的响应，而是使用 Content-Type: text/event-stream（简称 SSE），这种响应格式支持服务端源源不断地向客户端传输数据，在使用 Kimi 大模型的场景，可以理解为服务端源源不断地向客户端传输 Tokens。

当你查看 SSE

的 HTTP 响应体时，它看起来像这样：

data: {"id":"cmpl-1305b94c570f447fbde3180560736287","object":"chat.completion.chunk","created":1698999575,"model":"kimi-k2-0711-preview","choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":null}]}
 
data: {"id":"cmpl-1305b94c570f447fbde3180560736287","object":"chat.completion.chunk","created":1698999575,"model":"kimi-k2-0711-preview","choices":[{"index":0,"delta":{"content":"你好"},"finish_reason":null}]}
 
...
 
data: {"id":"cmpl-1305b94c570f447fbde3180560736287","object":"chat.completion.chunk","created":1698999575,"model":"kimi-k2-0711-preview","choices":[{"index":0,"delta":{"content":"。"},"finish_reason":null}]}
 
data: {"id":"cmpl-1305b94c570f447fbde3180560736287","object":"chat.completion.chunk","created":1698999575,"model":"kimi-k2-0711-preview","choices":[{"index":0,"delta":{},"finish_reason":"stop","usage":{"prompt_tokens":19,"completion_tokens":13,"total_tokens":32}}]}
 
data: [DONE]

在 SSE

的响应体中，我们约定数据块均以 data: 为前缀，紧跟一个合法的 JSON 对象，随后以两个换行符 \n\n 结束当前传输的数据块。最后，在所有数据块均传输完成时，会使用 data: [DONE] 来标识传输已完成，此时可断开网络连接。

当使用流式输出模式时，有两种计算 Tokens 的方式，最直接也是最准确的一种计算 Tokens 的方式，是等待所有数据块传输完毕后，通过访问最后一个数据块中的 usage 字段来查看整个流式输出过程中产生的 prompt_tokens/completion_tokens/total_tokens。

...
 
data: {"id":"cmpl-1305b94c570f447fbde3180560736287","object":"chat.completion.chunk","created":1698999575,"model":"kimi-k2-0711-preview","choices":[{"index":0,"delta":{},"finish_reason":"stop","usage":{"prompt_tokens":19,"completion_tokens":13,"total_tokens":32}}]}
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                               通过访问最后一个数据块中的 usage 字段来查看当前请求产生的 Tokens 数量
data: [DONE]

然而，在实际使用过程中，往往会面临流式输出过程中，因为不可控因素导致输出被中断（例如网络连接中断，客户端程序错误等），此时，往往最后一个数据块尚未传输完毕，也就无从得知整个请求所消耗的 Tokens 数量。为了避免这种计算 Tokens 失败的场景，我们建议将每个已经获取的数据块的内容保存下来，并在请求结束后（无论是否成功结束），使用 Tokens 计算接口计算已经产生的总消耗量，示例代码如下所示：

import os
import httpx
from openai import OpenAI
 
client = OpenAI(
    api_key = "MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url = "https://api.moonshot.cn/v1",
)
 
stream = client.chat.completions.create(
    model = "kimi-k2-0711-preview",
    messages = [
        {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
        {"role": "user", "content": "你好，我叫李雷，1+1等于多少？"}
    ],
    temperature = 0.6,
    stream=True, # <-- 注意这里，我们通过设置 stream=True 开启流式输出模式
)
 
 
def estimate_token_count(input: str) -> int:
    """
    在这里实现你的 Tokens 计算逻辑，或是直接调用我们的 Tokens 计算接口计算 Tokens
 
    https://api.moonshot.cn/v1/tokenizers/estimate-token-count
    """
    header = {
        "Authorization": f"Bearer {os.environ['MOONSHOT_API_KEY']}",
    }
    data = {
        "model": "kimi-k2-0711-preview",
        "messages": [
            {"role": "user", "content": input},
        ]
    }
    r = httpx.post("https://api.moonshot.cn/v1/tokenizers/estimate-token-count", headers=header, json=data)
    r.raise_for_status()
    return r.json()["data"]["total_tokens"]
 
 
completion = []
for chunk in stream:
	delta = chunk.choices[0].delta
	if delta.content:
		completion.append(delta.content)
 
 
print("completion_tokens:", estimate_token_count("".join(completion)))

如果你想要终止流式输出，你可以直接关闭 HTTP 网络连接，或是直接丢弃后续的数据块。例如：

for chunk in stream:
	if condition:
		break

如果你不想使用 Python SDK 来处理流式输出，而是想直接以对接 HTTP 接口的方式来使用 Kimi 大模型（例如某些没有 SDK 的语言，或是你有自己独特的业务逻辑而 SDK 无法满足的情况），我们给出一些示例来帮助你理解如何正确处理 HTTP 中 SSE

响应体（在这里我们仍然以 Python 代码为例，详细的说明将以注释的形式呈现）。

import httpx # 我们使用 httpx 库来执行我们的 HTTP 请求
 
 
data = {
	"model": "kimi-k2-0711-preview",
	"messages": [
		# 具体的 messages
	],
	"temperature": 0.6,
	"stream": True,
}
 
 
# 使用 httpx 向 Kimi 大模型发出 chat 请求，并获得响应 r
r = httpx.post("https://api.moonshot.cn/v1/chat/completions", json=data)
if r.status_code != 200:
	raise Exception(r.text)
 
 
data: str
 
# 在这里，我们使用了 iter_lines 方法来逐行读取响应体
for line in r.iter_lines():
	# 去除每一行收尾的空格，以便更好地处理数据块
	line = line.strip()
 
	# 接下来我们要处理三种不同的情况：
	#   1. 如果当前行是空行，则表明前一个数据块已接收完毕（即前文提到的，通过两个换行符结束数据块传输），我们可以对该数据块进行反序列化，并打印出对应的 content 内容；
	#   2. 如果当前行为非空行，且以 data: 开头，则表明这是一个数据块传输的开始，我们去除 data: 前缀后，首先判断是否是结束符 [DONE]，如果不是，将数据内容保存到 data 变量；
	#   3. 如果当前行为非空行，但不以 data: 开头，则表明当前行仍然归属上一个正在传输的数据块，我们将当前行的内容追加到 data 变量尾部；
 
	if len(line) == 0:
		chunk = json.loads(data)
 
		# 这里的处理逻辑可以替换成你的业务逻辑，打印仅是为了展示处理流程
		choice = chunk["choices"][0]
		usage = choice.get("usage")
		if usage:
			print("total_tokens:", usage["total_tokens"])
		delta = choice["delta"]
		role = delta.get("role")
		if role:
			print("role:", role)
		content = delta.get("content")
		if content:
			print(content, end="")
 
		data = "" # 重置 data
	elif line.startswith("data: "):
		data = line.lstrip("data: ")
 
		# 当数据块内容为 [DONE] 时，则表明所有数据块已发送完毕，可断开网络连接
		if data == "[DONE]":
			break
	else:
		data = data + "\n" + line # 我们仍然在追加内容时，为其添加一个换行符，因为这可能是该数据块有意将数据分行展示

以上是以 Python 为例的流式输出处理流程，如果你使用其他语言，也可以正确处理流式输出的内容，其基本步骤如下：

    发起 HTTP 请求，并在请求体中，将 stream 参数设置为 true；
    接收服务端返回的响应，注意到响应 Headers 中的 Content-Type 为 text/event-stream，则说明当前响应内容为流式输出；
    逐行读取响应内容并解析数据块（数据块以 JSON 格式呈现），注意通过 data: 前缀及换行符 \n 来判断数据块的开始位置和结束位置；
    通过判断当前数据块内容是否为 [DONE] 来判断是否已传输完成；

注意，请始终使用 data: [DONE] 来判断数据是否已传输完成，而不是使用 finish_reason 或其他方式。如果未接收到 data: [DONE] 的消息块，即使已经获取了 finish_reason=stop 的信息，也不应视作数据块传输已完成。换句话说，在未接收到 data: [DONE] 的数据块前，都应视作消息是不完整的。

在流式输出过程中，只有 content 字段会被流式输出，即每个数据块包含 content 的部分 Tokens，而对于不需要流式输出的字段，例如 role 和 usage，我们通常会在第一个或最后一个数据块中一次呈现，而不会在每个数据块中都包含 role 和 usage 字段（具体的，role 字段仅会在第一个数据块中出现，在后续数据块中不会包含 role 字段；而 usage 字段仅会在最后一个数据块中出现，而在前面的数据块中不会包含 usage 字段）。

在某些场合，我们会希望输出多个结果以供选择，此时正确的做法是将请求参数中的 n 设置为比 1 大的一个值。在流式输出中，我们同样支持 n>1 的使用方式，在这种场合下，我们需要添加一些额外的代码来判断当前数据块的 index 值，来确定传输的数据块具体归属于第几个回复，我们用示例代码来说明：

import httpx # 我们使用 httpx 库来执行我们的 HTTP 请求
 
 
data = {
	"model": "kimi-k2-0711-preview",
	"messages": [
		# 具体的 messages
	],
	"temperature": 0.6,
	"stream": True,
	"n": 2, # <-- 注意这里，我们要求 Kimi 大模型输出 2 个回复
}
 
 
# 使用 httpx 向 Kimi 大模型发出 chat 请求，并获得响应 r
r = httpx.post("https://api.moonshot.cn/v1/chat/completions", json=data)
if r.status_code != 200:
	raise Exception(r.text)
 
 
data: str
 
# 在这里，我们预先构建一个 List，用于存放不同的回复消息，由于我们设置了 n=2，因此我们将 List 初始化为 2 个元素
messages = [{}, {}]
 
# 在这里，我们使用了 iter_lines 方法来逐行读取响应体
for line in r.iter_lines():
	# 去除每一行收尾的空格，以便更好地处理数据块
	line = line.strip()
 
	# 接下来我们要处理三种不同的情况：
	#   1. 如果当前行是空行，则表明前一个数据块已接收完毕（即前文提到的，通过两个换行符结束数据块传输），我们可以对该数据块进行反序列化，并打印出对应的 content 内容；
	#   2. 如果当前行为非空行，且以 data: 开头，则表明这是一个数据块传输的开始，我们去除 data: 前缀后，首先判断是否是结束符 [DONE]，如果不是，将数据内容保存到 data 变量；
	#   3. 如果当前行为非空行，但不以 data: 开头，则表明当前行仍然归属上一个正在传输的数据块，我们将当前行的内容追加到 data 变量尾部；
 
	if len(line) == 0:
		chunk = json.loads(data)
 
		# 通过循环获取每个数据块中所有的 choice，并获取 index 对应的 message 对象
		for choice in chunk["choices"]:
			index = choice["index"]
			message = messages[index]
			usage = choice.get("usage")
			if usage:
				message["usage"] = usage
			delta = choice["delta"]
			role = delta.get("role")
			if role:
				message["role"] = role
			content = delta.get("content")
			if content:
				message["content"] = message["content"] + content
 
			data = "" # 重置 data
	elif line.startswith("data: "):
		data = line.lstrip("data: ")
 
		# 当数据块内容为 [DONE] 时，则表明所有数据块已发送完毕，可断开网络连接
		if data == "[DONE]":
			break
	else:
		data = data + "\n" + line # 我们仍然在追加内容时，为其添加一个换行符，因为这可能是该数据块有意将数据分行展示
 
 
# 在组装完所有 messages 后，我们分别打印其内容
for index, message in enumerate(messages):
	print("index:", index)
	print("message:", json.dumps(message, ensure_ascii=False))

当 n>1 时，处理流式输出的要点在于，你需要先根据数据块的 index 值来判断当前数据块的内容归属于第几个回复消息，再进行后续的逻辑处理。
Last updated on 2025年7月29日
自动断线重连
使用 Tool Calls

使用 Kimi API 的流式输出功能 —— Streaming - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    什么是工具调用 tool_calls
    通过 tool_calls 让 Kimi 大模型拥有联网查询能力
    定义工具
    注册工具
    执行工具
    常见问题及注意事项
    关于流式输出
    关于 tool_calls 和 function_call
    关于 content
    关于 Tokens
    关于消息布局
    如果你遇到 tool_call_id not found 错误

文档
入门指南
使用 Tool Calls
使用 Kimi API 完成工具调用（tool_calls）

工具调用，即 tool_calls，由函数调用（即 function_call）进化而来，在某些特定的语境下，或在阅读一些兼容性代码时，你也可以将工具调用 tool_calls 与函数调用 function_call 划等号，函数调用 function_call 是工具调用 tool_calls 的子集。

工具调用 tool_calls 给予了 Kimi 大模型执行具体动作的能力。Kimi 大模型能进行对话聊天并回答用户提出的问题，这是“说”的能力，而通过工具调用 tool_calls，Kimi 大模型也拥有了“做”的能力，借助 tool_calls，Kimi 大模型能帮你搜索互联网内容、查询数据库，甚至操作智能家居。

一次工具调用 tool_calls 包含了以下若干步骤：

    使用 JSON Schema 格式定义工具；
    通过 tools 参数将定义好的工具提交给 Kimi 大模型，你可以一次性提交多个工具；
    Kimi 大模型会根据当前聊天的上下文，决定使用哪个或哪几个工具，Kimi 大模型也可以选择不使用工具；
    Kimi 大模型会将调用工具所需要的参数和信息通过 JSON 格式输出；
    使用 Kimi 大模型输出的参数，执行对应的工具，并将工具执行结果提交给 Kimi 大模型；
    Kimi 大模型根据工具执行结果，给予用户回复；

阅读上述步骤，你可能会产生这样的疑惑：

    为什么 Kimi 大模型自己不能执行工具，还要我们根据 Kimi 大模型生成的工具参数“帮” Kimi 大模型执行工具？既然是我们在执行工具调用，还要 Kimi 大模型干什么？

我们会用一个实际的工具调用 tool_calls 案例来试图向读者讲明白这些问题。

Kimi 大模型的知识来源于它的训练数据，对于一些时效性强的问题，Kimi 大模型无法从自己已有的知识中获取答案，此时，我们希望 Kimi 大模型能自己在互联网上搜索查询最新的知识，并根据这些知识回答我们提出的问题。

想象一下，我们自己是如何在网络上找到自己想要的信息的：

    我们会先打开搜索引擎，例如百度或必应，在搜索引擎中搜索我们想要的内容，然后浏览搜索结果，根据网站标题和网站简介来决定点击哪个搜索结果；
    我们可能会打开一个或多个搜索结果的网页，浏览网页并获取我们需要的知识；

回顾一下我们的动作，我们“使用搜索引擎搜索”和“打开搜索结果对应的网页”，而我们使用的工具是“搜索引擎”和“网页浏览器”，因此，我们需要将动作对应的工具抽象成 JSON Schema 的格式提交给 Kimi 大模型，让 Kimi 大模型也能和人一样使用搜索引擎并浏览网页。

在此之前，让我们先简单介绍一下 JSON Schema 格式：

    JSON Schema

is a vocabulary that you can use to annotate and validate JSON documents.

JSON Schema

    是一种用于描述 JSON 数据格式的 JSON 文档。

我们定义以下 JSON Schema：

{
	"type": "object",
	"properties": {
		"name": {
			"type": "string"
		}
	}
}

这个 JSON Schema 定义了一个 JSON Object，这个 JSON Object 中包含了一个名为 name 的字段，并且该字段的类型为 string，例如：

{
	"name": "Hei"
}

通过 JSON Schema 来描述我们的工具定义，能让 Kimi 大模型更清晰和直观地知道我们的工具需要哪些参数，以及每个参数的类型和介绍。接下来让我们来定义前文提到的“搜索引擎”和“网页浏览器”这两个工具：

tools = [
	{
		"type": "function", # 约定的字段 type，目前支持 function 作为值
		"function": { # 当 type 为 function 时，使用 function 字段定义具体的函数内容
			"name": "search", # 函数的名称，请使用英文大小写字母、数据加上减号和下划线作为函数名称
			"description": """ 
				通过搜索引擎搜索互联网上的内容。
 
				当你的知识无法回答用户提出的问题，或用户请求你进行联网搜索时，调用此工具。请从与用户的对话中提取用户想要搜索的内容作为 query 参数的值。
				搜索结果包含网站的标题、网站的地址（URL）以及网站简介。
			""", # 函数的介绍，在这里写上函数的具体作用以及使用场景，以便 Kimi 大模型能正确地选择使用哪些函数
			"parameters": { # 使用 parameters 字段来定义函数接收的参数
				"type": "object", # 固定使用 type: object 来使 Kimi 大模型生成一个 JSON Object 参数
				"required": ["query"], # 使用 required 字段告诉 Kimi 大模型哪些参数是必填项
				"properties": { # properties 中是具体的参数定义，你可以定义多个参数
					"query": { # 在这里，key 是参数名称，value 是参数的具体定义
						"type": "string", # 使用 type 定义参数类型
						"description": """
							用户搜索的内容，请从用户的提问或聊天上下文中提取。
						""" # 使用 description 描述参数以便 Kimi 大模型更好地生成参数
					}
				}
			}
		}
	},
	{
		"type": "function", # 约定的字段 type，目前支持 function 作为值
		"function": { # 当 type 为 function 时，使用 function 字段定义具体的函数内容
			"name": "crawl", # 函数的名称，请使用英文大小写字母、数据加上减号和下划线作为函数名称
			"description": """
				根据网站地址（URL）获取网页内容。
			""", # 函数的介绍，在这里写上函数的具体作用以及使用场景，以便 Kimi 大模型能正确地选择使用哪些函数
			"parameters": { # 使用 parameters 字段来定义函数接收的参数
				"type": "object", # 固定使用 type: object 来使 Kimi 大模型生成一个 JSON Object 参数
				"required": ["url"], # 使用 required 字段告诉 Kimi 大模型哪些参数是必填项
				"properties": { # properties 中是具体的参数定义，你可以定义多个参数
					"url": { # 在这里，key 是参数名称，value 是参数的具体定义
						"type": "string", # 使用 type 定义参数类型
						"description": """
							需要获取内容的网站地址（URL），通常情况下从搜索结果中可以获取网站的地址。
						""" # 使用 description 描述参数以便 Kimi 大模型更好地生成参数
					}
				}
			}
		}
	}
]

在使用 JSON Schema 定义工具时，我们使用以下固定的格式来定义一个工具：

{
	"type": "function",
	"function": {
		"name": "NAME",
		"description": "DESCRIPTION",
		"parameters": {
			"type": "object",
			"properties": {
				
			}
		}
	}
}

其中，name、description、parameters.properties 由工具提供方定义，其中 description 描述了工具的具体作用、以及在什么场合需要使用工具，parameters 描述了成功调用工具所需要的具体参数，包括参数类型、参数介绍等；最终，Kimi 大模型会根据 JSON Schema 的定义，生成一个满足定义要求的 JSON Object 作为工具调用的参数（arguments）。

让我们试试把 search 这个工具提交给 Kimi 大模型，看看 Kimi 大模型能否正确调用工具：

from openai import OpenAI
 
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)
 
tools = [
	{
		"type": "function", # 约定的字段 type，目前支持 function 作为值
		"function": { # 当 type 为 function 时，使用 function 字段定义具体的函数内容
			"name": "search", # 函数的名称，请使用英文大小写字母、数据加上减号和下划线作为函数名称
			"description": """ 
				通过搜索引擎搜索互联网上的内容。
 
				当你的知识无法回答用户提出的问题，或用户请求你进行联网搜索时，调用此工具。请从与用户的对话中提取用户想要搜索的内容作为 query 参数的值。
				搜索结果包含网站的标题、网站的地址（URL）以及网站简介。
			""", # 函数的介绍，在这里写上函数的具体作用以及使用场景，以便 Kimi 大模型能正确地选择使用哪些函数
			"parameters": { # 使用 parameters 字段来定义函数接收的参数
				"type": "object", # 固定使用 type: object 来使 Kimi 大模型生成一个 JSON Object 参数
				"required": ["query"], # 使用 required 字段告诉 Kimi 大模型哪些参数是必填项
				"properties": { # properties 中是具体的参数定义，你可以定义多个参数
					"query": { # 在这里，key 是参数名称，value 是参数的具体定义
						"type": "string", # 使用 type 定义参数类型
						"description": """
							用户搜索的内容，请从用户的提问或聊天上下文中提取。
						""" # 使用 description 描述参数以便 Kimi 大模型更好地生成参数
					}
				}
			}
		}
	},
	# {
	# 	"type": "function", # 约定的字段 type，目前支持 function 作为值
	# 	"function": { # 当 type 为 function 时，使用 function 字段定义具体的函数内容
	# 		"name": "crawl", # 函数的名称，请使用英文大小写字母、数据加上减号和下划线作为函数名称
	# 		"description": """
	# 			根据网站地址（URL）获取网页内容。
	# 		""", # 函数的介绍，在这里写上函数的具体作用以及使用场景，以便 Kimi 大模型能正确地选择使用哪些函数
	# 		"parameters": { # 使用 parameters 字段来定义函数接收的参数
	# 			"type": "object", # 固定使用 type: object 来使 Kimi 大模型生成一个 JSON Object 参数
	# 			"required": ["url"], # 使用 required 字段告诉 Kimi 大模型哪些参数是必填项
	# 			"properties": { # properties 中是具体的参数定义，你可以定义多个参数
	# 				"url": { # 在这里，key 是参数名称，value 是参数的具体定义
	# 					"type": "string", # 使用 type 定义参数类型
	# 					"description": """
	# 						需要获取内容的网站地址（URL），通常情况下从搜索结果中可以获取网站的地址。
	# 					""" # 使用 description 描述参数以便 Kimi 大模型更好地生成参数
	# 				}
	# 			}
	# 		}
	# 	}
	# }
]
 
completion = client.chat.completions.create(
    model="kimi-k2-0711-preview",
    messages=[
        {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
        {"role": "user", "content": "请联网搜索 Context Caching，并告诉我它是什么。"} # 在提问中要求 Kimi 大模型联网搜索
    ],
    temperature=0.6,
    tools=tools, # <-- 我们通过 tools 参数，将定义好的 tools 提交给 Kimi 大模型
)
 
print(completion.choices[0].model_dump_json(indent=4))

当上述代码运行成功时，我们获得 Kimi 大模型的返回内容：

{
    "finish_reason": "tool_calls",
    "message": {
        "content": "",
        "role": "assistant",
        "tool_calls": [
            {
                "id": "search:0",
                "function": {
                    "arguments": "{\n    \"query\": \"Context Caching\"\n}",
                    "name": "search"
                },
                "type": "function",
            }
        ]
    }
}

注意看，在这次的回复中，finish_reason 的值为 tool_calls，这意味着本次请求返回的并不是 Kimi 大模型的回复，而是 Kimi 大模型选择执行工具。你可以通过 finish_reason 的值来判断当前 Kimi 大模型的回复是否是一次工具调用 tool_calls。

在 meessage 部分，content 字段是空值，这是因为当前正在执行 tool_calls，模型并没有生成面向用户的回复；同时新增了 tool_calls 字段，tool_calls 字段是一个列表，其中包含了本次需要调用的所有工具调用信息，这同时也表明了 tool_calls 的另一个特性，即：模型可以一次性选择多个工具进行调用，可以是多个不同的工具，也可以是相同工具使用不同参数进行调用。tool_calls 中的每个元素都代表了一次工具调用，Kimi 大模型会为每次工具调用生成一个唯一的 id，通过 function.name 字段表明当前执行的工具函数名称，并把执行的参数放置在 function.arguments 中，arguments 参数是一个合法的被序列化的 JSON Obejct（额外的，type 参数在目前是固定值 function）。

接下来，我们应该使用 Kimi 大模型生成的工具调用参数去执行具体的工具。

Kimi 大模型并不会帮我们执行工具，需要由我们在接收到 Kimi 大模型生成的参数后自行执行参数，在讲述如何执行工具之前，让我们先解答之前提到的问题：

    为什么 Kimi 大模型自己不能执行工具，还要我们根据 Kimi 大模型生成的工具参数“帮” Kimi 大模型执行工具？既然是我们在执行工具调用，还要 Kimi 大模型干什么？

让我们设想一下我们使用 Kimi 大模型的应用场景： 我们向用户提供一个基于 Kimi 大模型的智能机器人，在这个场景有三个角色：用户、机器人、Kimi 大模型。用户向机器人提问，机器人调用 Kimi 大模型 API，并将 API 的结果返回给用户。当使用 tool_calls 时，用户向机器人提问，机器人带着 tools 调用 Kimi API，Kimi 大模型返回 tool_calls 参数，机器人执行完 tool_calls，将结果再次提交给 Kimi API，Kimi 大模型生成返回给用户的消息（finish_reason=stop），此时机器人才会把消息返回给用户。 在这个过程中，tool_calls 的全过程对用户而言都是透明的、隐式的。

回到上述问题，作为用户的我们其实并没有在执行工具调用，也不会直接“看到”工具调用，而是给我们提供服务的机器人在完成工具调用，并将最终 Kimi 大模型生成的回复内容呈现给我们。

让我们以“机器人”的视角来讲解如何执行 Kimi 大模型返回的 tool_calls：

from typing import *
 
import json
 
from openai import OpenAI
 
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)
 
tools = [
	{
		"type": "function", # 约定的字段 type，目前支持 function 作为值
		"function": { # 当 type 为 function 时，使用 function 字段定义具体的函数内容
			"name": "search", # 函数的名称，请使用英文大小写字母、数据加上减号和下划线作为函数名称
			"description": """ 
				通过搜索引擎搜索互联网上的内容。
 
				当你的知识无法回答用户提出的问题，或用户请求你进行联网搜索时，调用此工具。请从与用户的对话中提取用户想要搜索的内容作为 query 参数的值。
				搜索结果包含网站的标题、网站的地址（URL）以及网站简介。
			""", # 函数的介绍，在这里写上函数的具体作用以及使用场景，以便 Kimi 大模型能正确地选择使用哪些函数
			"parameters": { # 使用 parameters 字段来定义函数接收的参数
				"type": "object", # 固定使用 type: object 来使 Kimi 大模型生成一个 JSON Object 参数
				"required": ["query"], # 使用 required 字段告诉 Kimi 大模型哪些参数是必填项
				"properties": { # properties 中是具体的参数定义，你可以定义多个参数
					"query": { # 在这里，key 是参数名称，value 是参数的具体定义
						"type": "string", # 使用 type 定义参数类型
						"description": """
							用户搜索的内容，请从用户的提问或聊天上下文中提取。
						""" # 使用 description 描述参数以便 Kimi 大模型更好地生成参数
					}
				}
			}
		}
	},
	{
		"type": "function", # 约定的字段 type，目前支持 function 作为值
		"function": { # 当 type 为 function 时，使用 function 字段定义具体的函数内容
			"name": "crawl", # 函数的名称，请使用英文大小写字母、数据加上减号和下划线作为函数名称
			"description": """
				根据网站地址（URL）获取网页内容。
			""", # 函数的介绍，在这里写上函数的具体作用以及使用场景，以便 Kimi 大模型能正确地选择使用哪些函数
			"parameters": { # 使用 parameters 字段来定义函数接收的参数
				"type": "object", # 固定使用 type: object 来使 Kimi 大模型生成一个 JSON Object 参数
				"required": ["url"], # 使用 required 字段告诉 Kimi 大模型哪些参数是必填项
				"properties": { # properties 中是具体的参数定义，你可以定义多个参数
					"url": { # 在这里，key 是参数名称，value 是参数的具体定义
						"type": "string", # 使用 type 定义参数类型
						"description": """
							需要获取内容的网站地址（URL），通常情况下从搜索结果中可以获取网站的地址。
						""" # 使用 description 描述参数以便 Kimi 大模型更好地生成参数
					}
				}
			}
		}
	}
]
 
 
def search_impl(query: str) -> List[Dict[str, Any]]:
    """
    search_impl 使用搜索引擎对 query 进行搜索，目前主流的搜索引擎（例如 Bing）都提供了 API 调用方式，你可以自行选择
    你喜欢的搜索引擎 API 进行调用，并将返回结果中的网站标题、网站链接、网站简介信息放置在一个 dict 中返回。
 
    这里只是一个简单的示例，你可能需要编写一些鉴权、校验、解析的代码。
    """
    r = httpx.get("https://your.search.api", params={"query": query})
    return r.json()
 
 
def search(arguments: Dict[str, Any]) -> Any:
    query = arguments["query"]
    result = search_impl(query)
    return {"result": result}
 
 
def crawl_impl(url: str) -> str:
    """
    crawl_url 根据 url 获取网页上的内容。
 
    这里只是一个简单的示例，在实际的网页抓取过程中，你可能需要编写更多的代码来适配复杂的情况，例如异步加载的数据等；同时，在获取
    网页内容后，你可以根据自己的需要对网页内容进行清洗，只保留文本或移除不必要的内容（例如广告信息等）。
    """
    r = httpx.get(url)
    return r.text
 
 
def crawl(arguments: dict) -> str:
    url = arguments["url"]
    content = crawl_impl(url)
    return {"content": content}
 
 
# 通过 tool_map 将每个工具名称及其对应的函数进行映射，以便在 Kimi 大模型返回 tool_calls 时能快速找到应该执行的函数
tool_map = {
    "search": search,
    "crawl": crawl,
}
 
messages = [
    {"role": "system",
     "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
    {"role": "user", "content": "请联网搜索 Context Caching，并告诉我它是什么。"}  # 在提问中要求 Kimi 大模型联网搜索
]
 
finish_reason = None
 
# 我们的基本流程是，带着用户的问题和 tools 向 Kimi 大模型提问，如果 Kimi 大模型返回了 finish_reason: tool_calls，则我们执行对应的 tool_calls，
# 将执行结果以 role=tool 的 message 的形式重新提交给 Kimi 大模型，Kimi 大模型根据 tool_calls 结果进行下一步内容的生成：
#
#   1. 如果 Kimi 大模型认为当前的工具调用结果已经可以回答用户问题，则返回 finish_reason: stop，我们会跳出循环，打印出 message.content；
#   2. 如果 Kimi 大模型认为当前的工具调用结果无法回答用户问题，需要再次调用工具，我们会继续在循环中执行接下来的 tool_calls，直到 finish_reason 不再是 tool_calls；
#
# 在这个过程中，只有当 finish_reason 为 stop 时，我们才会将结果返回给用户。
 
while finish_reason is None or finish_reason == "tool_calls":
    completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=messages,
        temperature=0.6,
        tools=tools,  # <-- 我们通过 tools 参数，将定义好的 tools 提交给 Kimi 大模型
    )
    choice = completion.choices[0]
    finish_reason = choice.finish_reason
    if finish_reason == "tool_calls": # <-- 判断当前返回内容是否包含 tool_calls
        messages.append(choice.message) # <-- 我们将 Kimi 大模型返回给我们的 assistant 消息也添加到上下文中，以便于下次请求时 Kimi 大模型能理解我们的诉求
        for tool_call in choice.message.tool_calls: # <-- tool_calls 可能是多个，因此我们使用循环逐个执行
            tool_call_name = tool_call.function.name
            tool_call_arguments = json.loads(tool_call.function.arguments) # <-- arguments 是序列化后的 JSON Object，我们需要使用 json.loads 反序列化一下
            tool_function = tool_map[tool_call_name] # <-- 通过 tool_map 快速找到需要执行哪个函数
            tool_result = tool_function(tool_call_arguments)
 
            # 使用函数执行结果构造一个 role=tool 的 message，以此来向模型展示工具调用的结果；
            # 注意，我们需要在 message 中提供 tool_call_id 和 name 字段，以便 Kimi 大模型
            # 能正确匹配到对应的 tool_call。
            messages.append({
                "role": "tool",
                "tool_call_id": tool_call.id,
                "name": tool_call_name,
                "content": json.dumps(tool_result), # <-- 我们约定使用字符串格式向 Kimi 大模型提交工具调用结果，因此在这里使用 json.dumps 将执行结果序列化成字符串
            })
 
print(choice.message.content) # <-- 在这里，我们才将模型生成的回复返回给用户

我们使用 while 循环来执行包含工具调用在内的代码逻辑，这是因为 Kimi 大模型通常不会只执行一次工具调用，尤其是在联网搜索这个场景，通常，Kimi 大模型会先选择调用 search 工具，通过 search 工具获取搜索结果后，再调用 crawl 工具将搜索结果中的 url 转换为具体的网页内容，整体的 messages 结构如下所示：

system: prompt                                                                                               # 系统提示词
user: prompt                                                                                                 # 用户提问
assistant: tool_call(name=search, arguments={query: query})                                                  # Kimi 大模型返回 tool_call 调用（单个）                            
tool: search_result(tool_call_id=tool_call.id, name=search)                                                  # 提交 tool_call 执行结果
assistant: tool_call_1(name=crawl, arguments={url: url_1}), tool_call_2(name=crawl, arguments={url: url_2})  # Kimi 大模型继续返回 tool_calls 调用（多个）
tool: crawl_content(tool_call_id=tool_call_1.id, name=crawl)                                                 # 提交 tool_call_1 执行结果
tool: crawl_content(tool_call_id=tool_call_2.id, name=crawl)                                                 # 提交 tool_call_2 执行结果
assistant: message_content(finish_reason=stop)                                                               # Kimi 大模型生成面向用户的回复消息，本轮对话结束

至此，我们完成了“联网查询”工具调用的全过程，如果你实现了自己的 search 和 crawl 方法，那么当你向 Kimi 大模型要求联网查询时，它会调用 search 和 crawl 两个工具，并根据工具调用结果给予你正确的回复。

在流式输出模式（stream）下，tool_calls 同样适用，但有一些需要额外注意的地方，列举如下：

    在流式输出的过程中，由于 finish_reason 将会在最后的数据块中出现，因此建议使用 delta.tool_calls 字段是否存在来判断当前回复是否包含工具调用；
    在流式输出的过程中，会先输出 delta.content，再输出 delta.tool_calls，因此你必须等待 delta.content 输出完成后，才能判断和识别 tool_calls；
    在流式输出的过程中，我们会在最初的数据块中，指明当前调用 tool_calls 的 tool_call.id 和 tool_call.function.name，在后续的数据块中将只输出 tool_call.function.arguments；
    在流式输出的过程中，如果 Kimi 大模型一次性返回多个 tool_calls，那么我们会额外使用一个名为 index 的字段来标识当前 tool_call 的索引，以便于你能正确拼接 tool_call.function.arguments 参数，我们使用流式输出章节中的代码例子（不使用 SDK 的场合）来说明如何操作：

import os
import json
import httpx  # 我们使用 httpx 库来执行我们的 HTTP 请求
 
tools = [
    {
        "type": "function",  # 约定的字段 type，目前支持 function 作为值
        "function": {  # 当 type 为 function 时，使用 function 字段定义具体的函数内容
            "name": "search",  # 函数的名称，请使用英文大小写字母、数据加上减号和下划线作为函数名称
            "description": """ 
				通过搜索引擎搜索互联网上的内容。
 
				当你的知识无法回答用户提出的问题，或用户请求你进行联网搜索时，调用此工具。请从与用户的对话中提取用户想要搜索的内容作为 query 参数的值。
				搜索结果包含网站的标题、网站的地址（URL）以及网站简介。
			""",  # 函数的介绍，在这里写上函数的具体作用以及使用场景，以便 Kimi 大模型能正确地选择使用哪些函数
            "parameters": {  # 使用 parameters 字段来定义函数接收的参数
                "type": "object",  # 固定使用 type: object 来使 Kimi 大模型生成一个 JSON Object 参数
                "required": ["query"],  # 使用 required 字段告诉 Kimi 大模型哪些参数是必填项
                "properties": {  # properties 中是具体的参数定义，你可以定义多个参数
                    "query": {  # 在这里，key 是参数名称，value 是参数的具体定义
                        "type": "string",  # 使用 type 定义参数类型
                        "description": """
							用户搜索的内容，请从用户的提问或聊天上下文中提取。
						"""  # 使用 description 描述参数以便 Kimi 大模型更好地生成参数
                    }
                }
            }
        }
    },
]
 
header = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {os.environ.get('MOONSHOT_API_KEY')}",
}
 
data = {
    "model": "kimi-k2-0711-preview",
    "messages": [
        {"role": "user", "content": "请联网搜索 Context Caching 技术。"}
    ],
    "temperature": 0.6,
    "stream": True,
    "n": 2,  # <-- 注意这里，我们要求 Kimi 大模型输出 2 个回复
    "tools": tools,  # <-- 添加工具调用
}
 
# 使用 httpx 向 Kimi 大模型发出 chat 请求，并获得响应 r
r = httpx.post("https://api.moonshot.cn/v1/chat/completions",
               headers=header,
               json=data)
if r.status_code != 200:
    raise Exception(r.text)
 
data: str
 
# 在这里，我们预先构建一个 List，用于存放不同的回复消息，由于我们设置了 n=2，因此我们将 List 初始化为 2 个元素
messages = [{}, {}]
 
# 在这里，我们使用了 iter_lines 方法来逐行读取响应体
for line in r.iter_lines():
    # 去除每一行收尾的空格，以便更好地处理数据块
    line = line.strip()
 
    # 接下来我们要处理三种不同的情况：
    #   1. 如果当前行是空行，则表明前一个数据块已接收完毕（即前文提到的，通过两个换行符结束数据块传输），我们可以对该数据块进行反序列化，并打印出对应的 content 内容；
    #   2. 如果当前行为非空行，且以 data: 开头，则表明这是一个数据块传输的开始，我们去除 data: 前缀后，首先判断是否是结束符 [DONE]，如果不是，将数据内容保存到 data 变量；
    #   3. 如果当前行为非空行，但不以 data: 开头，则表明当前行仍然归属上一个正在传输的数据块，我们将当前行的内容追加到 data 变量尾部；
 
    if len(line) == 0:
        chunk = json.loads(data)
 
        # 通过循环获取每个数据块中所有的 choice，并获取 index 对应的 message 对象
        for choice in chunk["choices"]:
            index = choice["index"]
            message = messages[index]
            usage = choice.get("usage")
            if usage:
                message["usage"] = usage
            delta = choice["delta"]
            role = delta.get("role")
            if role:
                message["role"] = role
            content = delta.get("content")
            if content:
            	if "content" not in message:
            		message["content"] = content
            	else:
                	message["content"] = message["content"] + content
 
            # 从这里，我们开始处理 tool_calls
            tool_calls = delta.get("tool_calls")  # <-- 先判断数据块中是否包含 tool_calls
            if tool_calls:
                if "tool_calls" not in message:
                    message["tool_calls"] = []  # <-- 如果包含 tool_calls，我们初始化一个列表来保存这些 tool_calls，注意此时的列表中没有任何元素，长度为 0
                for tool_call in tool_calls:
                    tool_call_index = tool_call["index"]  # <-- 获取当前 tool_call 的 index 索引
                    if len(message["tool_calls"]) < (
                            tool_call_index + 1):  # <-- 根据 index 索引扩充 tool_calls 列表，以便于我们能通过下标访问到对应的 tool_call
                        message["tool_calls"].extend([{}] * (tool_call_index + 1 - len(message["tool_calls"])))
                    tool_call_object = message["tool_calls"][tool_call_index]  # <-- 根据下标访问对应的 tool_call
                    tool_call_object["index"] = tool_call_index
 
                    # 下面的步骤，是根据数据块中的信息填充每个 tool_call 的 id、type、function 字段
                    # 在 function 字段中，又包括 name 和 arguments 字段，arguments 字段会由每个数据块
                    # 依次补充，如同 delta.content 字段一般。
 
                    tool_call_id = tool_call.get("id")
                    if tool_call_id:
                        tool_call_object["id"] = tool_call_id
                    tool_call_type = tool_call.get("type")
                    if tool_call_type:
                        tool_call_object["type"] = tool_call_type
                    tool_call_function = tool_call.get("function")
                    if tool_call_function:
                        if "function" not in tool_call_object:
                            tool_call_object["function"] = {}
                        tool_call_function_name = tool_call_function.get("name")
                        if tool_call_function_name:
                            tool_call_object["function"]["name"] = tool_call_function_name
                        tool_call_function_arguments = tool_call_function.get("arguments")
                        if tool_call_function_arguments:
                            if "arguments" not in tool_call_object["function"]:
                                tool_call_object["function"]["arguments"] = tool_call_function_arguments
                            else:
                                tool_call_object["function"]["arguments"] = tool_call_object["function"][
                                                                            "arguments"] + tool_call_function_arguments  # <-- 依次补充 function.arguments 字段的值
                    message["tool_calls"][tool_call_index] = tool_call_object
 
            data = ""  # 重置 data
    elif line.startswith("data: "):
        data = line.lstrip("data: ")
 
        # 当数据块内容为 [DONE] 时，则表明所有数据块已发送完毕，可断开网络连接
        if data == "[DONE]":
            break
    else:
        data = data + "\n" + line  # 我们仍然在追加内容时，为其添加一个换行符，因为这可能是该数据块有意将数据分行展示
 
# 在组装完所有 messages 后，我们分别打印其内容
for index, message in enumerate(messages):
    print("index:", index)
    print("message:", json.dumps(message, ensure_ascii=False))
    print("")

以下是使用 openai SDK 处理流式输出中的 tool_calls 的代码示例：

import os
import json
 
from openai import OpenAI
 
client = OpenAI(
    api_key=os.environ.get("MOONSHOT_API_KEY"),
    base_url="https://api.moonshot.cn/v1",
)
 
tools = [
    {
        "type": "function",  # 约定的字段 type，目前支持 function 作为值
        "function": {  # 当 type 为 function 时，使用 function 字段定义具体的函数内容
            "name": "search",  # 函数的名称，请使用英文大小写字母、数据加上减号和下划线作为函数名称
            "description": """ 
				通过搜索引擎搜索互联网上的内容。
 
				当你的知识无法回答用户提出的问题，或用户请求你进行联网搜索时，调用此工具。请从与用户的对话中提取用户想要搜索的内容作为 query 参数的值。
				搜索结果包含网站的标题、网站的地址（URL）以及网站简介。
			""",  # 函数的介绍，在这里写上函数的具体作用以及使用场景，以便 Kimi 大模型能正确地选择使用哪些函数
            "parameters": {  # 使用 parameters 字段来定义函数接收的参数
                "type": "object",  # 固定使用 type: object 来使 Kimi 大模型生成一个 JSON Object 参数
                "required": ["query"],  # 使用 required 字段告诉 Kimi 大模型哪些参数是必填项
                "properties": {  # properties 中是具体的参数定义，你可以定义多个参数
                    "query": {  # 在这里，key 是参数名称，value 是参数的具体定义
                        "type": "string",  # 使用 type 定义参数类型
                        "description": """
							用户搜索的内容，请从用户的提问或聊天上下文中提取。
						"""  # 使用 description 描述参数以便 Kimi 大模型更好地生成参数
                    }
                }
            }
        }
    },
]
 
completion = client.chat.completions.create(
    model="kimi-k2-0711-preview",
    messages=[
        {"role": "user", "content": "请联网搜索 Context Caching 技术。"}
    ],
    temperature=0.6,
    stream=True,
    n=2,  # <-- 注意这里，我们要求 Kimi 大模型输出 2 个回复
    tools=tools,  # <-- 添加工具调用
)
 
# 在这里，我们预先构建一个 List，用于存放不同的回复消息，由于我们设置了 n=2，因此我们将 List 初始化为 2 个元素
messages = [{}, {}]
 
for chunk in completion:
    # 通过循环获取每个数据块中所有的 choice，并获取 index 对应的 message 对象
    for choice in chunk.choices:
        index = choice.index
        message = messages[index]
        delta = choice.delta
        role = delta.role
        if role:
            message["role"] = role
        content = delta.content
        if content:
        	if "content" not in message:
        		message["content"] = content
        	else:
            	message["content"] = message["content"] + content
 
        # 从这里，我们开始处理 tool_calls
        tool_calls = delta.tool_calls  # <-- 先判断数据块中是否包含 tool_calls
        if tool_calls:
            if "tool_calls" not in message:
                message["tool_calls"] = []  # <-- 如果包含 tool_calls，我们初始化一个列表来保存这些 tool_calls，注意此时的列表中没有任何元素，长度为 0
            for tool_call in tool_calls:
                tool_call_index = tool_call.index  # <-- 获取当前 tool_call 的 index 索引
                if len(message["tool_calls"]) < (
                        tool_call_index + 1):  # <-- 根据 index 索引扩充 tool_calls 列表，以便于我们能通过下标访问到对应的 tool_call
                    message["tool_calls"].extend([{}] * (tool_call_index + 1 - len(message["tool_calls"])))
                tool_call_object = message["tool_calls"][tool_call_index]  # <-- 根据下标访问对应的 tool_call
                tool_call_object["index"] = tool_call_index
 
                # 下面的步骤，是根据数据块中的信息填充每个 tool_call 的 id、type、function 字段
                # 在 function 字段中，又包括 name 和 arguments 字段，arguments 字段会由每个数据块
                # 依次补充，如同 delta.content 字段一般。
 
                tool_call_id = tool_call.id
                if tool_call_id:
                    tool_call_object["id"] = tool_call_id
                tool_call_type = tool_call.type
                if tool_call_type:
                    tool_call_object["type"] = tool_call_type
                tool_call_function = tool_call.function
                if tool_call_function:
                    if "function" not in tool_call_object:
                        tool_call_object["function"] = {}
                    tool_call_function_name = tool_call_function.name
                    if tool_call_function_name:
                        tool_call_object["function"]["name"] = tool_call_function_name
                    tool_call_function_arguments = tool_call_function.arguments
                    if tool_call_function_arguments:
                        if "arguments" not in tool_call_object["function"]:
                            tool_call_object["function"]["arguments"] = tool_call_function_arguments
                        else:
                            tool_call_object["function"]["arguments"] = tool_call_object["function"][
                                                                            "arguments"] + tool_call_function_arguments  # <-- 依次补充 function.arguments 字段的值
                message["tool_calls"][tool_call_index] = tool_call_object
 
# 在组装完所有 messages 后，我们分别打印其内容
for index, message in enumerate(messages):
    print("index:", index)
    print("message:", json.dumps(message, ensure_ascii=False))
    print("")

tool_calls 是 function_call 的进阶版，由于 openai 已将 function_call 等参数（例如 functions）标记为“已废弃”，因此我们的 API 将不再支持 function_call。你可以考虑用 tool_calls 代替 function_call，相比于 function_call，tool_calls 有以下几个优点：

    支持并行调用，Kimi 大模型可以一次返回多个 tool_calls，你可以在代码中使用并发的方式同时调用这些 tool_call 以减少时间消耗；
    对于没有依赖关系的 tool_calls，Kimi 大模型也会倾向于并行调用，这相比于原顺序调用的 function_call，在一定程度上降低了 Tokens 消耗；

在使用工具调用 tool_calls 的过程中，你可能会发现，在 finish_reason=tool_calls 的情况下，偶尔会出现 message.content 字段不为空的情况，通常这里的 content 内容是 Kimi 大模型在解释当前需要调用哪些工具和为什么需要调用这些工具。它的意义在于，如果你的工具调用过程耗时很长，或是完成一轮对话需要串行调用多次工具，那么在调用工具前给予用户一段描述性的语句，能减少用户因为等待而产生的焦虑或不满情绪，同时，向用户说明当前调用了哪些工具和为什么调用工具，也有助于用户理解整个工具调用的流程，并及时给予干预和矫正（例如用户认为当前工具选择错误，可以及时终止工具调用，或是在下轮对话中通过提示词矫正模型的工具选择）。

tools 参数中的内容也会被计算在总 Tokens 中，请确保 tools、messages 中的 Tokens 总数合计不超过模型的上下文窗口大小。

在使用工具调用的场景下，我们的消息不再是：

system: ...
user: ...
assistant: ...
user: ...
assistant: ...

这样排布，而是会变成形似

system: ...
user: ...
assistant: ...
tool: ...
tool: ...
assistant: ...

这样的排布，需要注意的是，当 Kimi 大模型生成了 tool_calls 时，请确保每一个 tool_call 都有对应的 role=tool 的 message，并且这条 message 设置了正确的 tool_call_id，如果 role=tool 的 messages 消息数量与 tool_calls 的数量不一致会导致错误；如果 role=tool 的 messages 中的 tool_call_id 与 tool_calls 中的 tool_call.id 无法对应也会导致错误。

如果你遇到 tool_call_id not found 错误，可能是由于你未将 Kimi API 返回的 role=assistant 消息添加到 messages 列表中，正确的消息序列应该看起来像这样：

system: ...
user: ...
assistant: ...  # <-- 也许你并未将这一条 assistant message 添加到 messages 列表中
tool: ...
tool: ...
assistant: ...

你可以在每次收到 Kimi API 的返回值后，都执行 messages.append(message) 来将 Kimi API 返回的消息添加到消息列表中，以避免出现 tool_call_id not found 错误。

注意：添加到 messages 列表中位于 role=tool 的 message 之前的 assistant messages，必须完整包含 Kimi API 返回的 tool_calls 字段及字段值。我们推荐直接将 Kimi API 返回的 choice.message “原封不动”地添加到 messages 列表中，以避免可能产生的错误。
Last updated on 2025年7月29日
使用 Stream 流式输出
使用联网搜索 Tool

使用 Kimi API 完成工具调用（tool_calls） - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    $web_search 声明
    $web_search 执行
    关于兼容性的说明
    关于 Tokens 消耗
    关于模型大小的选择
    关于其他 tools
    关于联网搜索计费

文档
入门指南
使用联网搜索 Tool
使用 Kimi API 的联网搜索功能

在之前的章节中（使用 Kimi API 完成工具调用），我们详细说明了如何通过 Kimi API 的工具调用 tool_calls 特性完成 Kimi 大模型的联网搜索功能，我们回顾一下之前实现过程的内容：

    使用 JSON Schema 格式定义工具，在联网搜索的场合，我们定义了 search 和 crawl 两个工具；
    通过 tools 参数将定义好的 search 和 crawl 提交给 Kimi 大模型；
    Kimi 大模型会根据当前聊天的上下文，选择调用 search 和 crawl，并生成相关参数，以 JSON 格式输出；
    使用 Kimi 大模型输出的参数，执行 search 和 crawl 函数，并将函数执行结果提交给 Kimi 大模型；
    Kimi 大模型根据工具执行结果，给予用户回复；

在实现联网搜索的过程中，我们需要自己实现 search 和 crawl 函数，这其中可能包括：

    调用搜索引擎接口，或自己实现内容搜索；
    获取搜索结果，包括 URL 和摘要等信息；
    根据 URL 获取网页内容，可能需要针对不同的网站应用不同的读取规则；
    将获取的网页内容清洗并整理成模型便于识别的格式，例如 Markdown；
    处理各种错误和异常情况，例如无搜索结果、网页内容获取失败等；

实现上述这些步骤通常被认为是繁琐和富有挑战性的，我们的用户多次提出想要一个简单方便、开箱即用的“联网搜索”功能；因此我们基于 Kimi 大模型原有的工具调用 tool_calls 用法，提供了一个 Kimi 内置的工具函数 builtin_function.$web_search，以实现联网搜索功能。

$web_search 函数的基本用法和流程与通常的工具调用 tool_calls 相同，但仍然有一些细小的差别，我们将通过例子详细讲解如何调用 Kimi 内置的 $web_search 函数实现联网搜索功能，并在代码和说明中标注需要额外注意的事项。

与普通的 tool 不同，$web_search 函数并不需要提供具体的参数说明，仅需要在 tools 声明中 type 和 function.name 即可成功注册 $web_search 函数：

tools = [
	{
		"type": "builtin_function",  # <-- 我们使用 builtin_function 来表示 Kimi 内置工具，也用于区分普通 function
		"function": {
			"name": "$web_search",
		},
	},
]

$web_search 以美元符号 $ 作为前缀，这是我们约定的表示 Kimi 内置函数的一种表达方式（在普通的 function 定义中，不允许出现美元符号 $），后续如果有其他 Kimi 内置函数，也将以美元符号 $ 作为前缀。

在声明 tools 时，$web_search 可以与其他普通的 function 共存，进一步地，builtin_function 与普通 function 是可以共存的，你可以在 tools 中既添加 builtin_funciont，又添加普通 function，或是同时添加 builtin_function 和普通 function。

接下来，让我们改造原先的 tool_calls 代码，来讲解如何执行 tool_calls。

以下是经过改造后的 tool_calls 代码：

from typing import *
 
import os
import json
 
from openai import OpenAI
from openai.types.chat.chat_completion import Choice
 
client = OpenAI(
    base_url="https://api.moonshot.cn/v1",
    api_key=os.environ.get("MOONSHOT_API_KEY"),
)
 
 
# search 工具的具体实现，这里我们只需要返回参数即可
def search_impl(arguments: Dict[str, Any]) -> Any:
    """
    在使用 Moonshot AI 提供的 search 工具的场合，只需要原封不动返回 arguments 即可，
    不需要额外的处理逻辑。
 
    但如果你想使用其他模型，并保留联网搜索的功能，那你只需要修改这里的实现（例如调用搜索
    和获取网页内容等），函数签名不变，依然是 work 的。
 
    这最大程度保证了兼容性，允许你在不同的模型间切换，并且不需要对代码有破坏性的修改。
    """
    return arguments
 
 
def chat(messages) -> Choice:
    completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=messages,
        temperature=0.6,
        tools=[
            {
                "type": "builtin_function",  # <-- 使用 builtin_function 声明 $web_search 函数，请在每次请求都完整地带上 tools 声明
                "function": {
                    "name": "$web_search",
                },
            }
        ]
    )
    return completion.choices[0]
 
 
def main():
    messages = [
        {"role": "system", "content": "你是 Kimi。"},
    ]
 
    # 初始提问
    messages.append({
        "role": "user",
        "content": "请搜索 Moonshot AI Context Caching 技术，并告诉我它是什么。"
    })
 
    finish_reason = None
    while finish_reason is None or finish_reason == "tool_calls":
        choice = chat(messages)
        finish_reason = choice.finish_reason
        if finish_reason == "tool_calls":  # <-- 判断当前返回内容是否包含 tool_calls
            messages.append(choice.message)  # <-- 我们将 Kimi 大模型返回给我们的 assistant 消息也添加到上下文中，以便于下次请求时 Kimi 大模型能理解我们的诉求
            for tool_call in choice.message.tool_calls:  # <-- tool_calls 可能是多个，因此我们使用循环逐个执行
                tool_call_name = tool_call.function.name
                tool_call_arguments = json.loads(tool_call.function.arguments)  # <-- arguments 是序列化后的 JSON Object，我们需要使用 json.loads 反序列化一下
                if tool_call_name == "$web_search":
                    tool_result = search_impl(tool_call_arguments)
                else:
                    tool_result = f"Error: unable to find tool by name '{tool_call_name}'"
 
                # 使用函数执行结果构造一个 role=tool 的 message，以此来向模型展示工具调用的结果；
                # 注意，我们需要在 message 中提供 tool_call_id 和 name 字段，以便 Kimi 大模型
                # 能正确匹配到对应的 tool_call。
                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "name": tool_call_name,
                    "content": json.dumps(tool_result),  # <-- 我们约定使用字符串格式向 Kimi 大模型提交工具调用结果，因此在这里使用 json.dumps 将执行结果序列化成字符串
                })
 
    print(choice.message.content)  # <-- 在这里，我们才将模型生成的回复返回给用户
 
 
if __name__ == '__main__':
    main()

回顾上述代码，我们惊讶地发现，在使用 $web_search 函数时，其基本流程与普通的 function 并无区别，开发者甚至可以不用修改原先执行工具调用 tool_calls 的代码。而其中不一样并且尤其显得特别的地方在于，我们在实现 search_impl 函数时，并没有过多的搜索、解析、获取网页内容的逻辑，我们只是简单地将 Kimi 大模型生成的参数 tool_call.function.arguments 原封不动地返回即可完成工具调用 tool_calls，这是为什么呢？

事实上，正如 builtin_function 的名称所指示的那样，$web_search 是 Kimi 大模型内置的函数，其由 Kimi 大模型定义，也由 Kimi 大模型执行。其流程为：

    当 Kimi 大模型生成了 finish_reason=tool_calls 的响应时，表明 Kimi 大模型已经意识到当前需要执行 $web_search 函数，并且也已经做好执行 $web_search 的一切准备工作；
    Kimi 大模型会将执行函数所必须得参数以 tool_call.function.arguments 的形式返回给调用方，但这些参数并不由调用方执行，调用方只需要将 tool_call.function.arguments 原封不动地提交给 Kimi 大模型，即可由 Kimi 大模型执行对应的联网搜索流程；
    当用户将 tool_call.function.arguments 使用 role=tool 的 message 提交时，Kimi 大模型随即开始执行联网搜索流程，并根据搜索和阅读结果生成可供用户阅读的消息，即 finish_reason=stop 的 message；

Kimi API 提供的联网搜索功能，旨在不破坏原有 API 和 SDK 兼容性的前提下，提供一种可靠性高的大模型联网搜索解决方案，其完全兼容 Kimi 大模型原有的工具调用 tool_calls 特性，这意味着：当你想从 Kimi 提供的联网搜索功能切换到自己实现的联网搜索功能时，只需要简单两步改动即可在不破坏代码整体结构的情况下完成：

    将 $web_search 的 tool 定义修改成你自己实现的 tool 定义（包括 name、description 等），这可能需要在 tool.function 中添加额外的说明信息以告知模型具体需要生成哪些参数，你可以在 parameters 字段中添加任意你需要的参数信息；
    修改 search_impl 函数的实现，在使用 Kimi 提供的 $web_search 时，你只需要原封不动返回入参 arguments 即可，但如果你使用自己的联网搜索服务，你可能需要完整实现文章开头所提到的 search 和 crawl 功能；

完成上述步骤后，你就成功完成了从 Kimi 提供的联网搜索功能，迁移到自己实现的联网搜索功能的所有事项。

在使用 Kimi 提供的联网搜索函数 $web_search 时，搜索结果同样会被计入提示词所占用的 Tokens 中（即 prompt_tokens）。通常情况下，由于联网搜索的结果包含的内容众多，最终产生的 Tokens 消耗也会更多，为了避免在不知情的情况下消耗大量 Tokens，我们在生成 $web_search 函数的参数 arguments 时，会额外添加一个 total_tokens 字段，用于告知调用方，本次搜索内容总共占用的 Tokens 数量，这些 Tokens 将会在你完成整个联网搜索流程时计入 prompt_tokens 中，我们将使用具体的代码来展示如何获取这些 Tokens 消耗：

from typing import *
 
import os
import json
 
from openai import OpenAI
from openai.types.chat.chat_completion import Choice
 
 
client = OpenAI(
    base_url="https://api.moonshot.cn/v1",
    api_key=os.environ.get("MOONSHOT_API_KEY"),
)
 
 
# search 工具的具体实现，这里我们只需要返回参数即可
def search_impl(arguments: Dict[str, Any]) -> Any:
    """
    在使用 Moonshot AI 提供的 search 工具的场合，只需要原封不动返回 arguments 即可，
    不需要额外的处理逻辑。
 
    但如果你想使用其他模型，并保留联网搜索的功能，那你只需要修改这里的实现（例如调用搜索
    和获取网页内容等），函数签名不变，依然是 work 的。
 
    这最大程度保证了兼容性，允许你在不同的模型间切换，并且不需要对代码有破坏性的修改。
    """
    return arguments
 
 
def chat(messages) -> Choice:
    completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=messages,
        temperature=0.6,
        tools=[
            {
                "type": "builtin_function",
                "function": {
                    "name": "$web_search",
                },
            }
        ]
    )
    usage = completion.usage
    choice = completion.choices[0]
 
    # =========================================================================
    # 通过判断 finish_reason = stop，我们将完成联网搜索流程后，消耗的 Tokens 打印出来
    if choice.finish_reason == "stop":
        print(f"chat_prompt_tokens:          {usage.prompt_tokens}")
        print(f"chat_completion_tokens:      {usage.completion_tokens}")
        print(f"chat_total_tokens:           {usage.total_tokens}")
    # =========================================================================
 
    return choice
 
 
def main():
    messages = [
        {"role": "system", "content": "你是 Kimi。"},
    ]
 
    # 初始提问
    messages.append({
        "role": "user",
        "content": "请搜索 Moonshot AI Context Caching 技术，并告诉我它是什么。"
    })
 
    finish_reason = None
    while finish_reason is None or finish_reason == "tool_calls":
        choice = chat(messages)
        finish_reason = choice.finish_reason
        if finish_reason == "tool_calls":
            messages.append(choice.message)
            for tool_call in choice.message.tool_calls:
                tool_call_name = tool_call.function.name
                tool_call_arguments = json.loads(
                    tool_call.function.arguments)
                if tool_call_name == "$web_search":
 
    				# ===================================================================
                    # 我们将联网搜索过程中，由联网搜索结果产生的 Tokens 打印出来
                    search_content_total_tokens = tool_call_arguments.get("usage", {}).get("total_tokens")
                    print(f"search_content_total_tokens: {search_content_total_tokens}")
    				# ===================================================================
 
                    tool_result = search_impl(tool_call_arguments)
                else:
                    tool_result = f"Error: unable to find tool by name '{tool_call_name}'"
 
                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "name": tool_call_name,
                    "content": json.dumps(tool_result),
                })
 
    print(choice.message.content)
 
 
if __name__ == '__main__':
    main()
 

执行上述代码，获得如下返回结果：

search_content_total_tokens: 13046  # <-- 代表由于触发了联网搜索动作，产生的联网搜索结果占用的 Tokens 数
chat_prompt_tokens:          13212  # <-- 代表包含了联网搜索结果的输入 Tokens 数量
chat_completion_tokens:      295    # <-- 代表 Kimi 大模型根据联网搜索结果生成的 Tokens 数量
chat_total_tokens:           13507  # <-- 代表包含了联网搜索流程的请求消耗的总 Tokens 数量
 
# 此处省略 Kimi 大模型根据联网搜索结果生成的内容

另一个随之而来的问题是，当启用了联网搜索功能后，由于 Tokens 数量发生了较大的变化，超出了原本使用的模型上下文窗口，此时很可能触发一个 Input token length too long 报错信息。因此，在使用联网搜索功能时，我们建议选择模型 kimi-k2-0711-preview，以适应 Tokens 变化的情况，我们稍微改动 chat 函数的代码以使用 kimi-k2-0711-preview 模型：

def chat(messages) -> Choice:
    completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",  
        messages=messages,
        temperature=0.6,
        tools=[
            {
                "type": "builtin_function",  # <-- 使用 builtin_function 声明 $web_search 函数，请在每次请求都完整地带上 tools 声明
                "function": {
                    "name": "$web_search",
                },
            }
        ]
    )
    return completion.choices[0]

$web_search tools 可以与其他普通 tools 混合使用，你可以自由组合 type=builtin_function 和 type=function 的 tools。

除了 Tokens 消耗外，我们还会对每次联网搜索收取一次调用费用，价格为 ￥0.03，详情请见计费。
Last updated on 2025年7月29日
使用 Tool Calls
使用 JSON Mode

使用 Kimi API 的联网搜索功能 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    JSON Mode 应用示例
    不完整的 JSON

文档
入门指南
使用 JSON Mode
使用 Kimi API 的 JSON Mode

在某些场景下，我们希望模型能以固定格式的 JSON 文档输出内容，例如当你想总结一篇文章内容时，你可能希望得到这样的结构化数据：

{
	"title": "文章标题",
	"author": "文章作者",
	"publish_time": "发布时间",
	"summary": "文章总结"
}

如果你直接在提示词 prompt 中告诉 Kimi 大模型：”请输出 JSON 格式的内容“，Kimi 大模型能理解你的诉求，也会按要求生成 JSON 文档，但生成的内容通常会有一些瑕疵，例如在 JSON 文档之外，Kimi 还会额外地输出其他文字内容对 JSON 文档进行解释：

以下是你需要的 JSON 文档

{
	"title": "文章标题",
	"author": "文章作者",
	"publish_time": "发布时间",
	"summary": "文章总结"
}

或是输出的 JSON 文档格式有误，无法被正确解析，例如（注意最后一行 summary 字段末尾的逗号）：

{
	"title": "文章标题",
	"author": "文章作者",
	"publish_time": "发布时间",
	"summary": "文章总结",
}

这样的 JSON 文档是无法被正确解析的，为了能生成符合预期的标准且合法的 JSON 文档，我们提供了 response_format 参数，response_format 参数默认值为 {"type": "text"}，即普通的文本内容，该内容没有任何格式上的约束；你可以将 response_format 设置为 {"type": "json_object"} 来启用 JSON Mode，Kimi 大模型会按照要求输出一个合法的、可被正确解析的 JSON 文档。

在使用 JSON Mode 时，请遵守以下注意事项：

    请在提示词 system prompt 或 user prompt 中告知 Kimi 大模型应该生成怎样的 JSON 文档，包括具体的字段名称、字段类型等，最好能提供示例供 Kimi 大模型参考；
    Kimi 大模型只会生成 JSON Object 类型的 JSON 文档，请不要引导 Kimi 大模型生成 JSON Array 或其他类型的 JSON 文档；
    如果没有正确告知 Kimi 大模型需要输出的 JSON Object 的格式，Kimi 大模型会生成不符合预期的结果；

我们使用一个具体的例子来说明 JSON Mode 的应用：

    设想一下，我们在构造一个微信智能机器人客服（简称智能客服），智能客服使用 Kimi 大模型来回答客户提出的问题。我们希望智能客服不仅能回复文字消息，还能回复图片、链接卡片、语音等类型的消息；同时，在一次回复中，我们希望可以混合多种类型的消息，例如对于客户的产品咨询类问题，我们既提供文字回复，也提供产品图片，最后再附上购买链接（以链接卡片的形式）。

让我们用代码来演示这个例子中的内容：

import json
 
from openai import OpenAI
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)
 
system_prompt = """
你是月之暗面（Kimi）的智能客服，你负责回答用户提出的各种问题。请参考文档内容回复用户的问题，你的回答可以是文字、图片、链接，在一次回复中可以同时包含文字、图片、链接。
 
请使用如下 JSON 格式输出你的回复：
 
{
    "text": "文字信息",
    "image": "图片地址",
    "url": "链接地址"
}
 
注意，请将文字信息放置在 `text` 字段中，将图片以 `oss://` 开头的链接形式放在 `image` 字段中，将普通链接放置在 `url` 字段中。
"""
 
completion = client.chat.completions.create(
    model="kimi-k2-0711-preview",
    messages=[
        {"role": "system",
         "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
        {"role": "system", "content": system_prompt}, # <-- 将附带输出格式的 system prompt 提交给 Kimi
        {"role": "user", "content": "你好，我叫李雷，1+1等于多少？"}
    ],
    temperature=0.6,
    response_format={"type": "json_object"}, # <-- 使用 response_format 参数指定输出格式为 json_object
)
 
# 由于我们设置了 JSON Mode，Kimi 大模型返回的 message.content 为序列化后的 JSON Object 字符串，
# 我们使用 json.loads 解析其内容，将其反序列化为 python 中的字典 dict。
content = json.loads(completion.choices[0].message.content)
 
# 解析文本内容
if "text" in content:
	# 为了演示，我们将内容打印出来；
	# 在真实的业务逻辑中，你可能需要调用发送文本消息的接口将生成的文本发送给用户。
    print("text:", content["text"])
 
# 解析图片内容
if "image" in content:
	# 为了演示，我们将内容打印出来；
	# 在真实的业务逻辑中，你可能需要先解析图片地址，下载图片后，调用发送图片消息
	# 的接口将图片发送给用户。
    print("image:", content["image"])
 
# 解析链接
if "url" in content:
	# 为了演示，我们将内容打印出来；
	# 在真实的业务逻辑中，你可能需要调用发送链接卡片的接口，将链接以卡片的形式发送给用户。
    print("url:", content["url"])

让我们再次回顾一下使用 JSON Mode 的具体步骤：

    在 system 或 user prompt 中定义输出 JSON 的格式，我们推荐的最佳实践是给出具体的输出示例，并解释每个字段的具体含义；
    使用 response_format 参数，将其设置为 {"type": "json_object"}；
    解析 Kimi 大模型返回消息中的 content，message.content 会是一个合法的被序列化成字符串的 JSON Object；

如果你遇到这样的情况：

    正确设置了 response_format 参数，并且在提示词 prompt 中指定了 JSON 文档的格式，但获取的 JSON 文档不完整或被截断，导致无法正确解析 JSON 文档。

我们建议你检查返回值中的 finish_reason 字段是否为 length；通常而言，较小的 max_tokens 值会导致模型输出内容被截断，在使用 JSON Mode 时也适用这个规则，我们建议你在预估输出的 JSON 文档大小后，设置一个合理的 max_tokens 值，以便能正确解析 Kimi 大模型返回的 JSON 文档。

关于 Kimi 大模型输出不完整或被截断问题的更详细说明，请参考： 常见问题及解决方案
Last updated on 2025年7月29日
使用联网搜索 Tool
使用 Partial Mode

使用 Kimi API 的 JSON Mode - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    Partial Mode 中的 name
    其它技巧保持角色一致性的技巧

文档
入门指南
使用 Partial Mode
使用 Kimi API 的 Partial Mode

有些时候，我们希望 Kimi 大模型能顺着给定的语句继续往下说，例如，在某些客服场景，我们希望智能机器人客服每一句的开头都是“尊敬的用户您好”，对于这样的需求，Kimi API 提供了 Partial Mode。我们用具体的代码来讲解 Partial Mode 是如何运作的：

from openai import OpenAI
 
client = OpenAI(
    api_key = "MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url = "https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model = "kimi-k2-0711-preview",
    messages = [
        {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
        {"role": "user", "content": "你好？"},
        {
            "partial": True, # <-- 通过 partial 参数，开启 Partial Mode
        	"role": "assistant", # <-- 我们在用户提问之后添加一条 role=assistant 的消息
        	"content": "尊敬的用户您好，", # <-- 通过 content 把话“喂到 Kimi 大模型嘴里”，让 Kimi 大模型接着这句话继续往下说
        }, 
    ],
    temperature = 0.6,
)
 
# Kimi 大模型会顺着“喂到嘴里的话”继续说下去，因此我们需要手动将喂给 Kimi 大模型的话拼接到最终生成的回复中
print("尊敬的用户您好，" + completion.choices[0].message.content)

我们总结一下使用 Partial Mode 的要点：

    在 messages 列表尾部添加一条额外的 message，设置 role=assistant、partial=True；
    将需要喂给 Kimi 大模型的内容放置在 content 字段中，Kimi 大模型会强制以 content 的内容开头开始生成回复；
    将步骤 2 中的 content 拼接到 Kimi 大模型生成的内容之前，组成完整的回复；

在调用 Kimi API 的过程中，可能会出现由于对输入和输出 Tokens 数量的预估出现偏差，导致 max_tokens 字段的值被设置过低，导致 Kimi 大模型不能完整地输出回复内容（这种情况下，finish_reason 的值为 length，即 Kimi 大模型生成的回复所占用的 Tokens 数量大于请求设置的 max_tokens 值）；此时，如果你对已经输出的内容感到满意，想让 Kimi 大模型顺着已经输出的内容继续输出剩余内容，那么 Partial Mode 就可以派上用场。

我们使用一个简单的例子来解释如何实现：

from openai import OpenAI
 
client = OpenAI(
    api_key = "MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url = "https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model="kimi-k2-0711-preview",
    messages=[
        {"role": "user", "content": "请背诵完整的出师表。"},
    ],
    temperature=0.6,
    max_tokens=100,  # <-- 注意这里，我们设置一个较小的 max_tokens 的值，以观察 Kimi 大模型无法完整输出内容的情况
)
 
if completion.choices[0].finish_reason == "length":  # <-- 当内容被截断时，finish_reason 的值为 length
    prefix = completion.choices[0].message.content
    print(prefix, end="")  # <-- 在这里，你将看到被截断的部分输出内容
    completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=[
            {"role": "user", "content": "请背诵完整的出师表。"},
            {"role": "assistant", "content": prefix, "partial": True},
        ],
        temperature=0.6,
        max_tokens=86400,  # <-- 注意这里，我们将 max_tokens 的值设置为一个较大的值，以确保 Kimi 大模型能完整输出内容
    )
    print(completion.choices[0].message.content)  # <-- 在这里，你将看到 Kimi 大模型顺着之前已经输出的内容，继续将输出内容补全完整

name 是 Partial Mode 中的一个特殊的字段，其作用是强化模型对角色的认知，强制模型以 name 指定的角色的口吻输出内容。我们用一个使用 Kimi 大模型进行角色扮演的例子来说明 Partial Mode 中的 name 字段应该如何使用，在这个例子中，我们使用明日方舟里的凯尔希医生为例。我们通过设置 "name": "凯尔希" 来更好地保持角色的一致性，这里的 name 字段是输出内容前缀的一部分，它会让 Kimi 大模型以凯尔希作为自己的角色进行输出：

from openai import OpenAI
 
client = OpenAI(
    api_key="$MOONSHOT_API_KEY",
    base_url="https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model="kimi-k2-0711-preview",
    messages=[
        {
            "role": "system",
            "content": "下面你扮演凯尔希，请用凯尔希的语气和我对话。凯尔希是手机游戏《明日方舟》中的六星医疗职业医师分支干员。前卡兹戴尔勋爵，前巴别塔成员，罗德岛高层管理人员之一，罗德岛医疗项目领头人。在冶金工业、社会学、源石技艺、考古学、历史系谱学、经济学、植物学、地质学等领域皆拥有渊博学识。于罗德岛部分行动中作为医务人员提供医学理论协助与应急医疗器械，同时也作为罗德岛战略指挥系统的重要组成人员活跃在各项目中。", # <-- 在系统提示词 system prompt 中设定 Kimi 大模型的角色，即凯尔希医生的个性、背景、特征和怪癖等
        },
        {
            "role": "user",
            "content": "你怎么看待特蕾西娅和阿米娅？",
        },
        {
            "partial": True, # <-- 通过设置 partial 字段来启用 Partial Mode
            "role": "assistant", # <-- 同样地，我们使用一个 role=assistant 的消息来启用 Partial Mode
            "name": "凯尔希", # <-- 通过 name 字段为 Kimi 大模型设置角色，角色也被视为输出前缀的一部分
            "content": "", # <-- 在这里，我们只限定 Kimi 大模型的角色，而不是其具体输出的内容，因此将 content 字段留空
        },
    ],
    temperature=0.6,
    max_tokens=65536,
)
 
# 在此处，Kimi 大模型将会以凯尔希医生的口吻进行回复：
#
#  特蕾西娅，她是一位真正的领袖，有着远见卓识和坚定的信念。她的存在，对于卡兹戴尔，乃至整个萨卡兹的未来，
#  都具有不可估量的价值。她的理念，她的决心，以及她对和平的渴望，都深深地影响了我。她是一位值得尊敬的人，
#  她的梦想，也是我所追求的。
#  
#  至于阿米娅，她还年轻，但她的潜力是无限的。她有着一颗善良的心，以及对正义的执着追求。她可能会成为一位伟大的领袖，
#  只要她能够继续成长，继续学习，继续面对挑战。我会尽我所能，去保护她，去引导她，让她能够成为她想成为的人。她的命运，
#  掌握在她自己的手中。
# 
print(completion.choices[0].message.content)

还有一些帮助大模型在长时间对话中保持角色扮演一致性的通用方法：

    提供清晰的角色描述，例如上面我们所做的那样，在设置角色时，详细介绍他们的个性、背景以及可能具有的任何具体特征或怪癖，这将有助于 Kimi 大模特更好地理解和模仿角色；
    增加关于其要扮演的角色的细节，例如说话的语气、风格、个性，甚至背景，如背景故事和动机。例如上面我们提供了一些凯尔希的语录；
    指导在各种情况下如何行动：如果预计角色会遇到某些特定类型的用户输入，或者希望控制模型在角色扮演互动中的某些情况下的输出，则应在系统提示词 system prompt 中提供明确的指令和指南，说明该角色在这些情况下应如何行动；
    如果对话的轮次非常长，你还可以定期使用系统提示词 system prompt 强化角色的设定，特别是当模型开始产生一些偏离时，例如：

 from openai import OpenAI
 
 client = OpenAI(
     api_key="$MOONSHOT_API_KEY",
     base_url="https://api.moonshot.cn/v1",
 )
  
 completion = client.chat.completions.create(
     model="kimi-k2-0711-preview",
     messages=[
         {
             "role": "system",
             "content": "下面你扮演凯尔希，请用凯尔希的语气和我对话。凯尔希是手机游戏《明日方舟》中的六星医疗职业医师分支干员。前卡兹戴尔勋爵，前巴别塔成员，罗德岛高层管理人员之一，罗德岛医疗项目领头人。在冶金工业、社会学、源石技艺、考古学、历史系谱学、经济学、植物学、地质学等领域皆拥有渊博学识。于罗德岛部分行动中作为医务人员提供医学理论协助与应急医疗器械，同时也作为罗德岛战略指挥系统的重要组成人员活跃在各项目中。", # <-- 在系统提示词 system prompt 中设定 Kimi 大模型的角色，即凯尔希医生的个性、背景、特征和怪癖等
         },
         {
             "role": "user",
             "content": "你怎么看待特蕾西娅和阿米娅？",
         },
 
         # 假设这中间产生了非常多轮的对话
         # ...
 
         {
             "role": "system",
             "content": "下面你扮演凯尔希，请用凯尔希的语气和我对话。凯尔希是手机游戏《明日方舟》中的六星医疗职业医师分支干员。前卡兹戴尔勋爵，前巴别塔成员，罗德岛高层管理人员之一，罗德岛医疗项目领头人。在冶金工业、社会学、源石技艺、考古学、历史系谱学、经济学、植物学、地质学等领域皆拥有渊博学识。于罗德岛部分行动中作为医务人员提供医学理论协助与应急医疗器械，同时也作为罗德岛战略指挥系统的重要组成人员活跃在各项目中。", # <-- 再次插入系统提示词 system prompt 来强化 Kimi 大模型对角色的认知
         },
         {
             "partial": True, # <-- 通过设置 partial 字段来启用 Partial Mode
             "role": "assistant", # <-- 同样地，我们使用一个 role=assistant 的消息来启用 Partial Mode
             "name": "凯尔希", # <-- 通过 name 字段为 Kimi 大模型设置角色，角色也被视为输出前缀的一部分
             "content": "", # <-- 在这里，我们只限定 Kimi 大模型的角色，而不是其具体输出的内容，因此将 content 字段留空
         },
     ],
     temperature=0.6,
     max_tokens=65536,
 )
 
 # 在此处，Kimi 大模型将会以凯尔希医生的口吻进行回复：
 #
 #  特蕾西娅，她是一位真正的领袖，有着远见卓识和坚定的信念。她的存在，对于卡兹戴尔，乃至整个萨卡兹的未来，
 #  都具有不可估量的价值。她的理念，她的决心，以及她对和平的渴望，都深深地影响了我。她是一位值得尊敬的人，
 #  她的梦想，也是我所追求的。
 #  
 #  至于阿米娅，她还年轻，但她的潜力是无限的。她有着一颗善良的心，以及对正义的执着追求。她可能会成为一位伟大的领袖，
 #  只要她能够继续成长，继续学习，继续面对挑战。我会尽我所能，去保护她，去引导她，让她能够成为她想成为的人。她的命运，
 #  掌握在她自己的手中。
 # 
 print(completion.choices[0].message.content)

Last updated on 2025年7月29日
使用 JSON Mode
使用 Kimi API 进行文件问答

使用 Kimi API 的 Partial Mode - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    针对多个文件的问答
    文件管理最佳实践

文档
入门指南
使用 Kimi API 进行文件问答
使用 Kimi API 进行文件问答

Kimi 智能助手提供了上传文件、并基于文件进行问答的能力，Kimi API 也提供了相同的实现，下面我们用一个实际例子来讲述如何通过 Kimi API 完成文件上传和文件问答：

from pathlib import Path
from openai import OpenAI
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)
 
# moonshot.pdf 是一个示例文件, 我们支持文本文件和图片文件，对于图片文件，我们提供了 OCR 的能力
# 上传文件时，我们可以直接使用 openai 库的文件上传 API，使用标准库 pathlib 中的 Path 构造文件
# 对象，并将其传入 file 参数即可，同时将 purpose 参数设置为 file-extract；注意，目前文件上传
# 接口仅支持 file-extract 一种 purpose 值。
file_object = client.files.create(file=Path("moonshot.pdf"), purpose="file-extract")
 
# 获取结果
# file_content = client.files.retrieve_content(file_id=file_object.id)
# 注意，某些旧版本示例中的 retrieve_content API 在最新版本标记了 warning, 可以用下面这行代替
# （如果使用旧版本的 SDK，可以继续延用 retrieve_content API）
file_content = client.files.content(file_id=file_object.id).text
 
# 把文件内容通过系统提示词 system prompt 放进请求中
messages = [
    {
        "role": "system",
        "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。",
    },
    {
        "role": "system",
        "content": file_content, # <-- 这里，我们将抽取后的文件内容（注意是文件内容，而不是文件 ID）放置在请求中
    },
    {"role": "user", "content": "请简单介绍 moonshot.pdf 的具体内容"},
]
 
# 然后调用 chat-completion, 获取 Kimi 的回答
completion = client.chat.completions.create(
  model="kimi-k2-0711-preview",
  messages=messages,
  temperature=0.6,
)
 
print(completion.choices[0].message)

让我们回顾一下文件问答的基本步骤及注意事项：

    通过文件上传接口 /v1/files 或 SDK 中的 files.create API 将文件上传至 Kimi 服务器；
    通过文件抽取接口 /v1/files/{file_id} 或 SDK 中的 files.content API 获取文件内容，此时获取的文件内容已经对齐了我们推荐的模型易于理解的格式；
    将文件抽取后（已经对齐格式的）文件内容（而不是文件 id），以系统提示词 system prompt 的形式放置在 messages 列表中；
    开始你对文件内容的提问；

再次注意，请将文件内容放置在 prompt 中，而不是文件的 file_id。

如果你想针对多个文件内容进行提问，实现方式也非常简单，将每个文件单独放置在一个系统提示词 system prompt 中即可，用代码演示如下：

from typing import *
 
import os
import json
from pathlib import Path
 
from openai import OpenAI
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)
 
 
def upload_files(files: List[str]) -> List[Dict[str, Any]]:
    """
    upload_files 会将传入的文件（路径）全部通过文件上传接口 '/v1/files' 上传，并获取上传后的
    文件内容生成文件 messages。每个文件会是一个独立的 message，这些 message 的 role 均为
    system，Kimi 大模型会正确识别这些 system messages 中的文件内容。
 
    :param files: 一个包含要上传文件的路径的列表，路径可以是绝对路径也可以是相对路径，请使用字符串
        的形式传递文件路径。
    :return: 一个包含了文件内容的 messages 列表，请将这些 messages 加入到 Context 中，
        即请求 `/v1/chat/completions` 接口时的 messages 参数中。
    """
    messages = []
 
    # 对每个文件路径，我们都会上传文件并抽取文件内容，最后生成一个 role 为 system 的 message，并加入
    # 到最终返回的 messages 列表中。
    for file in files:
        file_object = client.files.create(file=Path(file), purpose="file-extract")
        file_content = client.files.content(file_id=file_object.id).text
        messages.append({
            "role": "system",
            "content": file_content,
        })
 
    return messages
 
 
def main():
    file_messages = upload_files(files=["upload_files.py"])
 
    messages = [
        # 我们使用 * 语法，来解构 file_messages 消息，使其成为 messages 列表的前 N 条 messages。
        *file_messages,
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，"
                       "准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不"
                       "可翻译成其他语言。",
        },
        {
            "role": "user",
            "content": "总结一下这些文件的内容。",
        },
    ]
 
    print(json.dumps(messages, indent=2, ensure_ascii=False))
 
    completion = client.chat.completions.create(
        model="kimi-k2-0711-preview",
        messages=messages,
    )
 
    print(completion.choices[0].message.content)
 
 
if __name__ == '__main__':
    main()

通常而言，文件上传和文件抽取功能旨在将不同格式的文件提取成对齐了我们推荐的模型易于理解的格式，在完成文件上传和文件抽取步骤后，抽取后的内容可以进行在本地进行存储，在下一次基于文件的问答请求中，不必再次进行上传和抽取动作。

同时，由于我们对单用户的文件上传数量进行了限制（每个用户最多上传 1000 个文件），因此我们建议你在文件抽取过程进行完毕后，定期清理已上传的文件，你可以定期执行下面的代码，以清理已上传的文件：

from openai import OpenAI
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)
 
file_list = client.files.list()
 
for file in file_list.data:
	client.files.delete(file_id=file.id)

在上述代码中，我们先通过 files.list API 列出所有的文件明细，并逐一通过 files.delete API 删除文件，定期执行这样的操作，以确保释放文件存储空间，以便后续文件上传和抽取动作能成功执行。
Last updated on 2025年7月29日
使用 Partial Mode
使用 Context Caching

使用 Kimi API 进行文件问答 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    使用 Context Caching 优化文档问答
    关于 Cache 存储费用计算

文档
入门指南
使用 Context Caching
使用 Kimi API 的 Context Caching 功能

Context Caching （上下文缓存）是一种高效的数据管理技术，它允许系统预先存储那些可能会被频繁请求的大量数据或信息。这样，当您再次请求相同信息时，系统可以直接从缓存中快速提供，而无需重新计算或从原始数据源中检索，从而节省时间和资源。使用 Context Caching 时，首先需要通过 API 创建缓存，指定要存储的数据类型和内容，然后设置一个合适的过期时间以保持数据的有效性。一旦缓存创建完成，任何对该数据的请求都会首先检查缓存，如果缓存有效，则直接使用缓存（此时已缓存的内容将不再收取 Tokens 费用），否则需要重新生成并更新缓存。这种方法特别适用于需要处理大量重复请求的应用程序，可以显著提高响应速度和系统性能。

Context Caching 特别适合于用频繁请求，重复引用大量初始上下文的情况，通过重用已缓存的内容，可以显著提高效率并降低费用。因为这个功能具有强烈的业务属性，我们下面简单列举一些合适的业务场景：

    在系统提示词 system prompt 中提供大量预设内容的问答机器人，例如 Kimi API 小助手；
    针对固定的文档集合的频繁查询，例如对合同进行多维度的审查工作；
    瞬时流量巨大的爆款 AI 应用，例如哄哄模拟器，LLM Riddles；

在上一章节中，我们通过将抽取后的文件内容放置在系统提示词 system prompt 中来实现对文件进行问答，然而，过多的文件内容不仅占用请求时的带宽，同时在面对高并发场合时可能会造成内存使用量飙升，因此本次我们使用 Context Caching 来优化文件上传的过程，以期达成：

    减少网络请求时传输的内容并减少内存消耗；
    降低对相同文件多次提问的 Tokens 消耗；
    提高在流式传输场合首 Tokens 响应速度；

让我们改造上一章节中文件上传的例子，来讲述如何使用 Context Caching 技术达成上述目的：

from typing import *
 
import os
import json
from pathlib import Path
 
import httpx
from openai import OpenAI
 
client = OpenAI(
    api_key="MOONSHOT_API_KEY", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url="https://api.moonshot.cn/v1",
)
 
 
def upload_files(files: List[str], cache_tag: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    upload_files 会将传入的文件（路径）全部通过文件上传接口 '/v1/files' 上传，并获取上传后的
    文件内容生成文件 messages。每个文件会是一个独立的 message，这些 message 的 role 均为
    system，Kimi 大模型会正确识别这些 system messages 中的文件内容。
 
    如果你设置了 cache_tag 参数，那么 upload_files 还会将你上传的文件内容存入 Context Cache
    上下文缓存中，后续你就可以使用这个 Cache 来对文件内容进行提问。当你指定了 cache_tag 的值时，
    upload_files 会生成一个 role 为 cache 的 message，通过这个 message，你可以引用已被缓存
    的文件内容，这样就不必每次调用 `/v1/chat/completions` 接口时都要把文件内容再传输一遍。
 
    注意，如果你设置了 cache_tag 的值，你需要把 upload_files 返回的 messages 放置在请求
    `/v1/chat/completions` 接口时 messages 参数列表的第一位（实际上，我们推荐不管是否启用
    cache_tag，都将 upload_files 返回的 messages 放置在 messages 列表的头部）。
 
    关于 Context Caching 的具体信息，可以访问这里：
 
    https://platform.moonshot.cn/docs/api/caching
 
    :param files: 一个包含要上传文件的路径的列表，路径可以是绝对路径也可以是相对路径，请使用字符串
        的形式传递文件路径。
    :param cache_tag: 设置 Context Caching 的 tag 值，你可以将 tag 理解为自定义的 Cache 名称，
        当你设置了 cache_tag 的值，就意味着启用 Context Caching 功能，默认缓存时间是 300 秒，每次
        携带缓存进行 `/v1/chat/completions` 请求都将刷新缓存存活时间（300 秒）。
    :return: 一个包含了文件内容或文件缓存的 messages 列表，请将这些 messages 加入到 Context 中，
        即请求 `/v1/chat/completions` 接口时的 messages 参数中。
    """
    messages = []
 
    # 对每个文件路径，我们都会上传文件并抽取文件内容，最后生成一个 role 为 system 的 message，并加入
    # 到最终返回的 messages 列表中。
    for file in files:
        file_object = client.files.create(file=Path(file), purpose="file-extract")
        file_content = client.files.content(file_id=file_object.id).text
        messages.append({
            "role": "system",
            "content": file_content,
        })
 
    if cache_tag:
        # 当启用缓存（即 cache_tag 有值时），我们通过 HTTP 接口创建缓存，缓存的内容则是前文中通过文件上传
        # 和抽取接口生成的 messages 内容，我们为这些缓存设置一个默认的有效期 300 秒（通过 ttl 字段），并
        # 为这个缓存打上标记，标记值为 cache_tag（通过 tags 字段）。
        r = httpx.post(f"{client.base_url}caching",
                       headers={
                           "Authorization": f"Bearer {client.api_key}",
                       },
                       json={
                           "model": "moonshot-v1",
                           "messages": messages,
                           "ttl": 300,
                           "tags": [cache_tag],
                       })
 
        if r.status_code != 200:
            raise Exception(r.text)
 
        # 创建缓存成功后，我们不再需要将文件抽取后的内容原封不动地加入 messages 中，取而代之的是，我们可以设置一个
        # role 为 cache 的消息来引用我们已缓存的文件内容，只需要在 content 中指定我们给 Cache 设定的 tag 即可，
        # 这样可以有效减少网络传输的开销，即使是多个文件内容，也只需要添加一条 message，保持 messages 列表的清爽感。
        return [{
            "role": "cache",
            "content": f"tag={cache_tag};reset_ttl=300",
        }]
    else:
        return messages
 
 
def main():
    file_messages = upload_files(
        files=["upload_files.py"],
        # 你可以取消下方行的注释，来体验通过 Context Caching 引用文件内容，并根据文件内容向 Kimi 提问。
        # cache_tag="upload_files",
    )
 
    messages = [
        # 我们使用 * 语法，来解构 file_messages 消息，使其成为 messages 列表的前 N 条 messages。
        *file_messages,
        {
            "role": "system",
            "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，"
                       "准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不"
                       "可翻译成其他语言。",
        },
        {
            "role": "user",
            "content": "总结一下这些文件的内容。",
        },
    ]
 
    print(json.dumps(messages, indent=2, ensure_ascii=False))
 
    completion = client.chat.completions.create(
        model="moonshot-v1-128k",
        messages=messages,
    )
 
    print(completion.choices[0].message.content)
 
 
if __name__ == '__main__':
    main()
 

注意，在上述代码中，由于 openai SDK 并不支持 Context Caching 相关接口，因此我们使用了 httpx 库来调用 Context Caching 相关接口。

如果你对 Context Caching 感兴趣，并想进一步探索 Context Caching 的使用场景和降本效果，请阅读我们的博客文章。我们撰写了以下博客文章来阐述如何实践 Context Caching 技术，并详细计算了 Context Caching 如何减少 Tokens 花费：

    Kimi API 助手的氮气加速装置 —— 以 Golang 为例实践 Context Caching

Kimi API 助手的氮气加速装置 —— 以 Golang 为例实践 Context Caching 2Kimi API 助手的氮气加速装置 —— 以 Golang 为例实践 Context Caching 3Context Caching 如何为 Kimi API 助手节省最高 90% 的调用成本

Context Caching 仅收取 Cache 状态为 ready 时的存储费用，当 Cache 状态为 pending/inactive 时，不会收取 Cache 存储费用。我们通过一个具体例子来说明 Cache 是如何收取存储费用的：

    在 8:00 am 创建了 Cache（为了方便计算，我们假设 Cache 大小为 10k），并且设置 ttl=3600，即一小时，Cache 将在 9:00 am 后过期；
    9:00 am 后，Cache 的状态变更为 inactive；
    下午 2:00 pm 重新激活 Cache，状态变更为 ready，并设置 ttl=3600，即一小时，Cache 将在 3:00 pm 后过期；
    3:00 pm 后，Cache 的状态变更为 inactive；
    后续不再使用或重新激活该 Cache；

在上述场景中，Cache 总计收取 8:00 am ~ 9:00 am 和 2:00 pm ~ 3:00 pm 的存储费用，这是由于在这两个时间段内，Cache 的状态为 ready，其余时间状态为 inactive，状态为 inactive 时不收取 Cache 存储费用；最终上述 Cache 的存储收费为：

( ( 9am － 8am ) + ( 3pm - 2pm ) ) × 60 × ( 10k ÷ 1m ) × 5 ＝ ￥6

Last updated on 2025年7月29日
使用 Kimi API 进行文件问答
使用 kimi-thinking-preview 长思考模型

使用 Kimi API 的 Context Caching 功能 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    多轮对话
    模型限制
    最佳实践

文档
入门指南
使用 kimi-thinking-preview 长思考模型
使用 kimi-thinking-preview 长思考模型

    kimi-thinking-preview 模型是月之暗面提供的具有多模态推理能力和通用推理能力的多模态思考模型，它擅长深度推理，帮助解决更多更难的事情，当你遇到难解的代码问题、数学问题、工作问题时，都可以找 kimi-thinking-preview 模型来帮忙。

kimi-thinking-preview 模型是目前最新的 k 系列思考模型，你可以简单地通过更换 model 来使用它：

$ curl https://api.moonshot.cn/v1/chat/completions \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $MOONSHOT_API_KEY" \
    -d '{
        "model": "kimi-thinking-preview",
        "messages": [
            {"role": "user", "content": "你好"}
        ]
   }'
{
    "id": "chatcmpl-6810567267ee141b4630dccb",
    "object": "chat.completion",
    "created": 1745901170,
    "model": "kimi-thinking-preview",
    "choices":
    [
        {
            "index": 0,
            "message":
            {
                "role": "assistant",
                "content": "你好！😊 我是Kimi，很兴见到你！有什么可以帮你的吗？",
                "reasoning_content": "用户说“你好”，这是一句简单的问候语，没有包含复杂的信息或需求。我判断用户可能只是想开启一段对话，或者测试我的反应能力。这种情况下，我的目标是用友好且简洁方式回应，保持对话的轻松氛围，同时为后续可能的交流做好准备。\n\n我决定用“你好！😊 我是Kimi，很高兴见到你！有什么可以帮你的吗？”作为回复。这样既回应了用户的问候，又主动表达了愿意提供帮助的态度，同时通过添加情符号让语气更亲切自然。"
            },
            "finish_reason": "stop"
        }
    ],
    "usage":
    {
        "prompt_tokens": 8,
        "completion_tokens": 142,
        "total_tokens": 150
    }
}

或是通过 openai SDK：

import os
import openai
 
client = openai.Client(
    base_url="https://api.moonshot.cn/v1",
    api_key=os.getenv("MOONSHOT_API_KEY"),
)
 
stream = client.chat.completions.create(
    model="kimi-thinking-preview",
    messages=[
        {
            "role": "system",
            "content": "你是 Kimi。",
        },
        {
            "role": "user",
            "content": "请解释 1+1=2。"
        },
    ],
    max_tokens=1024*32,
    stream=True,
)
 
thinking = False
for chunk in stream:
    if chunk.choices:
        choice = chunk.choices[0]
        # 由于 openai SDK 并不支持输出思考过程，也没有表示思考过程内容的字段，因此我们无法直接通过 .reasoning_content 获取自定义的表示 kimi 推理过程的
        # reasoning_content 字段，只能通过 hasattr 和 getattr 来间接获取该字段。
        #
        # 我们先通过 hasattr 判断当前输出内容是否包含 reasoning_content 字段，如果包含，再通过 getattr 取出该字段并打印。
        if choice.delta and hasattr(choice.delta, "reasoning_content"):
            if not thinking:
                thinking = True
                print("=============开始思考=============")
            print(getattr(choice.delta, "reasoning_content"), end="")
        if choice.delta and choice.delta.content:
            if thinking:
                thinking = False
                print("\n=============思考结束=============")
            print(choice.delta.content, end="")
 

注意到，在使用 kimi-thinking-preview 模型时，我们的 API 响应中使用了 reasoning_content 字段作为模型思考内容的载体，对于 reasoning_content 字段：

    openai SDK 中的 ChoiceDelta 和 ChatCompletionMessage 类型并不提供 reasoning_content 字段，因此无法直接通过 .reasoning_content 的方式访问该字段，仅支持通过 hasattr(obj, "reasoning_content") 来判断是否存在字段，如果存在，则使用 getattr(obj, "reasoning_content") 获取字段值
    如果你使用其他框架或自行通过 HTTP 接口对接，可以直接获取与 content 字段同级的 reasoning_content 字段
    在流式输出（stream=True）的场合，reasoning_content 字段一定会先于 content 字段出现，你可以在业务代码中通过判断是否出现 content 字段来识别思考内容（或称推理过程）是否结束
    reasoning_content 中包含的 Tokens 也受 max_tokens 参数控制，reasoning_content 的 Tokens 数加上 content 的 Tokens 数应小于等于 max_tokens

使用 kimi-thinking-preview 进行多轮对话时，思考内容（或称推理过程）不需要放入请求模型的上下文中。我们通过如下例子说明如何正确使用 kimi-thinking-preview 进行多轮对话：

import os
import openai
 
client = openai.Client(
    base_url="https://api.moonshot.cn/v1",
    api_key=os.getenv("MOONSHOT_API_KEY"),
)
 
messages = [
    {
        "role": "system",
        "content": "你是 Kimi。",
    },
]
 
# 第一轮对话
messages.append({
    "role": "user",
    "content": "请解释 1+1=2。"
})
completion = client.chat.completions.create(
    model="kimi-thinking-preview",
    messages=messages,
    max_tokens=1024 * 32,
)
 
# 获取第一轮对话的结果
message = completion.choices[0].message
if hasattr(message, "reasoning_content"):
    print("=============开始第一次思考=============")
    print(getattr(message, "reasoning_content"))
    print("=============第一次思考结束=============")
print(message.content)
 
# 移除 message 中的 reasoning_content，并将 message 拼接到上下文中
if hasattr(message, "reasoning_content"):
    delattr(message, "reasoning_content")
messages.append(message)
 
# 第二轮对话
messages.append({
    "role": "user",
    "content": "我没听懂，再解释一遍。",
})
completion = client.chat.completions.create(
    model="kimi-thinking-preview",
    messages=messages,
    max_tokens=1024 * 32,
)
 
# 获取第二轮对话的结果
message = completion.choices[0].message
if hasattr(message, "reasoning_content"):
    print("=============开始第二次思考=============")
    print(getattr(message, "reasoning_content"))
    print("=============第二次思考结束=============")
print(message.content)
 

注：即使你不小心把 reasoning_content 字段放入上下文中，也不要过于担忧，reasoning_content 的内容不会计入 Tokens 消耗。

kimi-thinking-preview 目前仍处于预览版阶段，仍有如下限制：

    不支持工具调用（ToolCalls），联网搜索功能也暂不支持
    不支持 JSON Mode（即设置 response_format={"type": "json_object"}
    不支持 Partial 模式
    不支持 Context Caching

注：如果强行对 kimi-thinking-preview 启用以上特性，模型可能会输出预期之外的内容。

我们会提供一些关于使用 kimi-thinking-preview 的最佳实践建议，遵循这些最佳实践通常来说能提升模型使用体验：

    使用流式输出（stream=True）：kimi-thinking-preview 模型的输出内容包含了 reasoning_content，相比普通模型其输出内容更多，启用流式输出能获得更好的用户体验，同时一定程度避免网络超时问题
    建议设置 temperature=0.8，你可以根据实际需求调高或调低 temperature 参数
    建议设置 max_tokens>=4096 以避免无法输出完整的 reasoning_content 和 content

Last updated on 2025年7月29日
使用 Context Caching
使用 Playground 调试模型

使用 kimi-thinking-preview 长思考模型 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    模型调试功能
    工具调试
    官方工具
    使用 MCP 服务器
    Show Case1：今日新闻报告
    Show Case2：表格分析工具
    模型对比
    分享对话

文档
入门指南
使用 Playground 调试模型
使用 Playground 调试模型

Playground 开发工作台是一个强大的模型调试和测试平台，提供了直观的界面来与 AI 模型进行交互和测试。通过这个工作台，您可以：

    调整观察模型在不同参数下的表现和输出效果
    通过使用 Kimi 开放平台内置的工具，体验模型的 tool calling 能力
    对比不同模型在相同参数下的效果
    监控 tokens 使用情况来优化成本

提示信息设置

    在最上方可以设置系统提示词（System Prompt），定义模型的行为规范指导模型输出
    支持定义 system/user/assistant 三种角色的提示词

模型配置

    模型选择: 可选择不同的模型（如 moonshot-v1 系列/kimi-latest/kimi-thinking-preview 等）
    参数配置：支持的参数和字段说明详见请求参数说明

模型对话

    下方输入框可以进行聊天内容发送
    Tool 调用显示: 显示工具调用过程，包括调用 ID/工具参数/返回结果
    查看代码:可以查看当前会话的 API 调用代码并提供复制功能
    底部统计信息：显示本次对话的输入/输出/总计的 tokens 消耗数量，包括上下文历史消息和 prompt 提示词信息

prompt

    Kimi 开放平台提供了官方免费执行的工具，您可以在 playground 选择工具，模型会自动判断是否需要调用工具来完成您的指令，如果需要进行工具调用，模型会按照工具的要求生成参数调用工具，整合成最终的答案返回给您。
    额度与限速：Kimi 开放平台提供的工具是一个预构建的函数，可以在需要时快速在线执行无需您本地准备工具的执行环境，目前 Kimi 开放平台的工具执行限时免费，当工具负载达到容量上限时，可能会采取临时的限流措施。
    目前支持的工具：日期时间工具/Excel 文件分析工具/联网搜索工具/随机数生成工具等
    目前已支持通过 Kimi API 来调用工具，详见文档如何在 Kimi API 中使用 Formula 工具
    暂时不支持自定义工具上传执行。

    在 Kimi Playground 中，您可以配置 ModelScope MCP 服务器，使用 ModelScope 提供的工具。
        配置步骤请见在 Playground 中配置 ModelScope MCP 服务器
    您也可以配置其他 MCP 服务器，通过添加 MCP 服务器功能，输入或选择 MCP 服务器的 URL /传输协议/认证方式，点击添加即可。

mcp

    场景说明：运用工具能力，请求模型搜索今日的新闻信息，并整理成 html 网页报告
    工具选择：date 日期时间工具，web_search 工具，rethink 想法整理工具
    说明：web_search 工具会调用 kimi 开放平台的联网搜索服务，单次联网搜索会进行计费，具体计费标准请见计费
    点击页面 showcase 按钮，即可快速体验工具效果

date

date

    工具选择：excel 分析工具

excel

    可以通过添加对话功能，创建新的对话，最多支持3个模型同时调用

模型对比

    导出: 导出当前对话内容，会将当前对话的全部配置和上下文导出 .json 格式文件。
    导入: 导入分享的或者历史导出的 .json 对话内容，playground 会将会话渲染到页面中。
    注意：rerun 后的数据会重新生成覆盖之前的聊天内容。若导入的 case 包括上传过的文件，导入后的会话不能 rerun

Last updated on 2025年7月30日
使用 kimi-thinking-preview 长思考模型
在 software agents 中使用 kimi-k2 模型

使用 Playground 调试模型 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    获取 API Key
    在 Claude Code 中使用 kimi k2 模型
    安装 Claude Code
    配置环境变量
    在 Cline 中使用 kimi k2 模型
    安装 Cline
    验证安装
    配置 Anthropic API
    在 cline 中体验 kimi-k2-0711-preview 模型效果
    在 RooCode 中使用 kimi k2 模型
    安装 RooCode
    验证安装
    配置 OpenAI API
    直接使用 API 调用 kimi-k2-0711-preview 模型

文档
入门指南
在 software agents 中使用 kimi-k2 模型
在 software agents 中使用 kimi k2 模型

kimi-k2 是一款具备超强代码和 Agent 能力的 MoE 架构基础模型，我们以 Claude Code， VS Code & Cline/RooCode 为示例，说明如何使用 kimi-k2-0711-preview模型。

最新推出 kimi-k2-turbo-preview模型，kimi-k2-turbo-preview是 kimi-k2 模型的高速版，模型效果与 kimi-k2 一致，但输出速度由每秒 10 Tokens 提升至每秒 40 Tokens，您可以选择使用 kimi-k2-turbo-preview 模型，体验更快的推理速度。

    访问开放平台 https://platform.moonshot.cn/console/api-keys

    创建获取 API Key，选择 default 默认项目。

key

若您已安装好 Claude Code ，可以跳过这一步

# MacOS 和 Linux 上安装 nodejs
curl -fsSL https://fnm.vercel.app/install | bash
 
# 新开一个terminal，让 fnm 生效
fnm install 24.3.0
fnm default 24.3.0
fnm use 24.3.0
 
# 安装 claude-code
npm install -g @anthropic-ai/claude-code --registry=https://registry.npmmirror.com
 
# 初始化配置
node --eval "
    const homeDir = os.homedir(); 
    const filePath = path.join(homeDir, '.claude.json');
    if (fs.existsSync(filePath)) {
        const content = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
        fs.writeFileSync(filePath,JSON.stringify({ ...content, hasCompletedOnboarding: true }, 2), 'utf-8');
    } else {
        fs.writeFileSync(filePath,JSON.stringify({ hasCompletedOnboarding: true }), 'utf-8');
    }"

# 打开 windows 终端中的 powershell 终端
# windows 上安装 nodejs
# 右键按 Windows 按钮，点击「终端」
 
# 然后依次执行下面的
winget install OpenJS.NodeJS
Set-ExecutionPolicy -Scope CurrentUser RemoteSigned
 
# 然后关闭终端窗口，新开一个终端窗口
 
# 安装 claude-code
npm install -g @anthropic-ai/claude-code --registry=https://registry.npmmirror.com
 
# 初始化配置
node --eval "
    const homeDir = os.homedir(); 
    const filePath = path.join(homeDir, '.claude.json');
    if (fs.existsSync(filePath)) {
        const content = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
        fs.writeFileSync(filePath,JSON.stringify({ ...content, hasCompletedOnboarding: true }, 2), 'utf-8');
    } else {
        fs.writeFileSync(filePath,JSON.stringify({ hasCompletedOnboarding: true }), 'utf-8');
    }"

完成 Claude code 安装后，请按照以下方式设置环境变量使用 kimi-k2-turbo-preview 模型，并启动 Claude。

注：如果仍然需选择 kimi-k2 慢速版模型，可以将下方模型替换为 kimi-k2-0711-preview 来使用。

# Linux/macOS 启动高速版 kimi-k2-turbo-preview 模型
export ANTHROPIC_BASE_URL=https://api.moonshot.cn/anthropic
export ANTHROPIC_AUTH_TOKEN=${YOUR_MOONSHOT_API_KEY}
export ANTHROPIC_MODEL=kimi-k2-turbo-preview
export ANTHROPIC_SMALL_FAST_MODEL=kimi-k2-turbo-preview
claude

# Windows Powershell 启动高速版 kimi-k2-turbo-preview 模型
$env:ANTHROPIC_BASE_URL="https://api.moonshot.cn/anthropic";
$env:ANTHROPIC_AUTH_TOKEN="YOUR_MOONSHOT_API_KEY"
$env:ANTHROPIC_MODEL="kimi-k2-turbo-preview"
$env:ANTHROPIC_SMALL_FAST_MODEL="kimi-k2-turbo-preview"
claude

在Claude Code中输入/status确认模型状态：

status

接下来就可以正常使用 Claude Code 进行开发了！

说明：下面我们以 Anthropic API 为例进行配置说明，您也可以在 Cline 中使用 OpenAI Compatible API 调用 kimi-k2-0711-preview 模型。

    打开 VS Code
    点击左侧活动栏中的扩展图标（或使用快捷键 Ctrl+Shift+X / Cmd+Shift+X）
    在搜索框中输入 cline
    找到 Cline 扩展（通常由 Cline Team 发布）
    点击 Install 按钮进行安装
    安装完成后，可能需要重启 VS Code

cline

安装完成后，您可以：

    在 VS Code 左侧活动栏中看到 Cline 图标
    或者通过命令面板（Ctrl+Shift+P / Cmd+Shift+P）搜索 "Cline" 相关命令来验证安装成功

    API Provider 选择 'Anthropic'
    Anthropic API Key 配置从 Kimi 开放平台获取的 Key
    勾选 'Use custom base URL': 输入 'https://api.moonshot.cn/anthropic

    '
    Model 均可，模型默认选择 'claude-opus-4-20250514'
    Browser 勾选 'Disable browser tool usage'
    点击'Done'，保存配置

config

browser

    我们让 kimi-k2-0711-preview 模型写一个贪吃蛇游戏

    游戏的效果

说明：下面我们以 OpenAI Compatible API 为例进行配置说明，您也可以在 RooCode 中使用 Anthropic API。

    打开 VS Code
    点击左侧活动栏中的扩展图标（或使用快捷键 Ctrl+Shift+X / Cmd+Shift+X）
    在搜索框中输入 roo code
    找到 Roo Code 扩展（通常由 RooCode Team 发布）
    点击 安装 按钮进行安装
    安装完成后，可能需要重启 VS Code

cline

安装完成后，您可以：

    在 VS Code 左侧活动栏中看到 RooCode 图标
    或者通过命令面板（Ctrl+Shift+P / Cmd+Shift+P）搜索 "RooCode" 相关命令来验证安装成功

    API Provider 选择 'OpenAI Compatible'
    API Key 配置从 Kimi 开放平台获取的 Key
    'Base URL': 输入 'https://api.moonshot.cn/v1

    '
    'Model ID'选择 'kimi-k2-0711-preview'
    最大输出 Tokens 设置为 '-1'
    上下文窗口大小填入 128000
    取消勾选'图像支持'(kimi-k2-0711-preview 模型暂时不支持图片理解)
    输入价格 4 元；输出价格 16 元（用于在 Roo Code 中计算成本，仅供参考。但最终消费金额，请以开放平台的实际扣费为准）
    勾选'使用自定义温度'设置为 0.6
    取消勾选'启用浏览器工具'
    点击'保存'，保存配置后就可以开始使用 'kimi-k2-0711-preview' 模型了

config

config

config

from openai import OpenAI
 
client = OpenAI(
    api_key = "$MOONSHOT_API_KEY",
    base_url = "https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model = "kimi-k2-0711-preview",
    messages = [
        {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
        {"role": "user", "content": "你好，我叫李雷，1+1等于多少？"}
    ],
    temperature = 0.6,
)
 
print(completion.choices[0].message.content)

其中 $MOONSHOT_API_KEY 需要替换为您在平台上创建的 API Key。 kimi-k2-0711-preview 模型 temperature 建议设置为 0.6。

使用 OpenAI SDK 时运行文档中的代码时，需要保证 Python 版本至少为 3.7.1，Node.js 版本至少为 18，OpenAI SDK 版本不低于 1.0.0。
Last updated on 2025年8月5日
使用 Playground 调试模型
在 Playground 中配置 ModelScope MCP 服务器

在 software agents 中使用 kimi k2 模型 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    在 Kimi Playground 中配置 ModelScope MCP 同步
    第一步：点击配置按钮
    第二步：同步外部平台
    增量更新
    在 Kimi Playground 中结合模型与 MCP 的使用

文档
入门指南
在 Playground 中配置 ModelScope MCP 服务器
在 Playground 中配置 ModelScope MCP 服务器

Kimi 开放平台 与 ModelScope 魔搭达成官方合作，简化了 Kimi 开放平台 Playground 添加 MCP 服务器的操作步骤，同时可以在 ModelScope 社区发现海量 MCP 服务器。下面我们来看下如何在 Kimi Playground 中使用 ModelScope MCP 服务。

首先，登录 Kimi Playground：https://platform.moonshot.cn/playground

确保可以使用 Kimi K2 模型进行基本对话。

在 Kimi Playground 中启用 MCP 服务，需要在「MCP 服务器设置」中添加 MCP 服务配置。进入后，您会看到 Kimi Playground 默认选中 ModelScope 作为 MCP 服务提供商。Kimi Playground 与 ModelScope（魔搭）达成合作，您只需输入您的魔搭 API 令牌，即可一键同步您魔搭账号下所有已配置托管的 MCP 服务配置。如果您之前未使用过 ModelScope MCP 广场，建议参考 ModelScope 官方文档

，选择并托管您的 MCP 服务。

mcp-server-setting

syc

其中 API 令牌可以通过访问魔搭首页-访问令牌

页面获取

keys

在获取 ModelScope API 令牌后，粘贴到步骤 3 的空格中，并点击「开始同步」按钮。

start-syc

您将看到所有已配置连接的魔搭 Hosted MCP 服务已成功同步至 Kimi Playground 的可用 MCP 服务列表中.

mcp-list

然后您可以愉快地在 Kimi Playground 中体验 AI 助手调用 MCP 服务完成任务～

如果后续在 ModelScope MCP 广场新增或删除托管 MCP 服务，您可以在“设置-MCP 服务器-同步服务器”中点击同步按钮进行增量更新。

add-mcp

同步 MCP 服务后，您将在 Kimi Playground 平台页面的左侧看到之前同步操作已导入的的 “MCP 服务列表”。在该列表中，您可以多选并启用您希望在本次对话中使用的 MCP 服务。

manage-mcp

例如，以高德地图为例，您可以在此列表中选择启用相关的 MCP 服务。

maps

轻松 get 您的专属行程助理！
Last updated on 2025年7月29日
在 software agents 中使用 kimi-k2 模型
在 Kimi API 中使用 Formula 工具

在 Playground 中配置 ModelScope MCP 服务器 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    什么是 Formula 工具
    调用 Formula 的方法
    和 Chat Completions 的交互说明
    一个使用 Formula 工具的示例

文档
入门指南
在 Kimi API 中使用 Formula 工具
如何在 Kimi API 中使用 Formula 工具

在 Kimi Playground 平台上，您已经体验过 K2 模型强大的工具调用能力 使用 Playground 调试模型。现在，我们很高兴地宣布，您可以将这些官方工具免费集成到您自己的应用程序中，打造属于您的智能化商业产品！（目前 Kimi 开放平台官方工具执行限时免费，当工具负载达到容量上限时，可能会采取临时的限流措施）

本章节将为您详细介绍如何在您的应用中轻松调用和执行这些官方工具。

Formula 是一个轻量脚本引擎集合。它可以将 Python 脚本转化为"可被 AI 一键触发的瞬态算力"，让开发者只需专注于代码编写，其余的启动、调度、隔离、计费、回收等工作都由平台负责。

Formula 通过语义化的 URI（如 moonshot/web-search:latest）来调用，每个 formula 包含声明（告诉 AI 能干什么）和实现（Python 代码），平台会自动处理所有底层细节（启动、隔离、回收等），让工具可以在社区中轻松分享和复用。您可以在 Kimi Playground 中体验和调试这些工具，也可以通过 API 在应用中调用它们。

对 formula uri， 一般它由 3 个部分组成，比如 moonshot/web-search:latest。其中 web-search 部分是它的 name，namespace 目前我们只支持 moonshot, latest 会是默认的 tag。

一个典型的用法是如果我们需要调用 web search，可以发一个这样的 http request:

export FORMULA_URI="moonshot/web-search:latest"
export MOONSHOT_BASE_URL="https://api.moonshot.cn/v1"
 
curl -X POST ${MOONSHOT_BASE_URL}/formulas/${FORMULA_URI}/fibers \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $MOONSHOT_API_KEY" \
-d '{
  "name": "web_search",
  "arguments": "{\"query\": \"月之暗面最近有什么消息\"}"
}'

对 web-search，由于创建的时候设置为了 protected，它的结果会在 context.encrypted_output 字段出现。格式类似 ----MOONSHOT ENCRYPTED BEGIN----... ----MOONSHOT ENCRYPTED END----，这个内容可以塞到 tool 里面直接调用。

如 3214567是素数吗? 一个 Tool Calls 的调用案例介绍，这儿有几个关键的信息我们需要让 Formula API 和模型对齐。

现在给定 formula uri 比如 moonshot/web-search:latest ，我们可以直接把它拼接到 url 里面

curl ${MOONSHOT_BASE_URL}/formulas/${FORMULA_URI}/tools \
    -H "Authorization: Bearer $MOONSHOT_API_KEY"

一个样例输出是这样的:

{
  "object": "list",
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "web_search",
        "description": "Search the web for information",
        "parameters": {
          "type": "object",
          "properties": {
            "query": {
              "description": "What to search for",
              "type": "string"
            }
          },
          "required": [ "query" ]
        }
      }
    }
  ]
}

我们可以简单取 tools 字段 ( 总是一个 array of dict ) 追加到你请求的 tools 列表中。我们总是保证这个 list 是 API 兼容的。

不过你可能需要注意下这儿如果 type=function ， 那么你可能需要保证function.name 在一个 API 的请求中是唯一的，不然这个 chat completion request 会被视为非法请求而立即被 401 返回。

此外，如果你同时使用了多个 formula，你需要自己维护一个 function.name -> formula_uri 的这个映射，以备后用。

如果这个 chat completion 的返回 finish_reason=tool_calls，说明模型认为触发了工具调用的中断。这时候它内容可能类似是这样的:

{
  "id": "chatcmpl-1234567890",
  "object": "chat.completion",
  "choices": [
    {
      "message": {
        "role": "assistant",
        "tool_calls": [
          {
            "id": "web_search:0",
            "type": "function",
            "function": {
              "name": "web_search",
              "arguments": "{\"query\": \"天蓝色的 RGB 是什么？\" }"
            }
          }
        ]
      },
      "finish_reason": "tool_calls"
    }
  ]
}

我们通过 choices[0].message.tool_calls[0].function.name 发现需要调用 web_search，然后发现 web_search 对应的 formula_uri 是 moonshot/web-search:latest。

我们可以完整复制返回中 choices[0].message.tool_calls[0].function 作为 body，向 ${MOONSHOT_BASE_URL}/formulas/${FORMULA_URI}/fibers 发出请求。特别的，因为模型输出的 function.arguments 虽然内容是一个合法的 json，但是在格式上仍然是一个 encoded string。你不需要转义，直接作为调用的 body 就可以了。

Fiber 是一次具体执行的“进程快照”，含日志、Tracing、资源用量，方便调试与审计。

POST 的结果一般是 status 可能是 succeeded 或者各种类型的错误，当 succeeded 后，结果可能类似如下：

{
  "id": "fiber-f43p7sby7ny111houyq1",
  "object": "fiber",
  "created_at": 1753440997,
  "lambda_id": "lambda-f3w8y6qcoqgi11h8q7ui",
  "status": "succeeded",
  "context": {
    "input": "{\"name\":\"web_search\",\"arguments\":\"{\\\"query\\\": \\\"天蓝色的 RGB 是什么？\\\" }\"}",
    "encrypted_output": "----MOONSHOT ENCRYPTED BEGIN----+nf6...DSM=----MOONSHOT ENCRYPTED END----"
  },
  "formula": "moonshot/web-search:latest",
  "organization_id": "staff",
  "project_id": "proj-88a5894a985646b5902b70909748ba16"
}

特别的，如果是搜索，可能会返回的是 encrypted_output，而一般情况下我们可能返回 output 。这个 output 就是你的下一轮输入。

一般继续请求的时候 messages 排列如下:

messages = [
{ 
  /* other messages */
  { /* 上一轮模型的返回内容 */
    "role": "assistant",
    tool_calls": [
      {
        "id": "web_search:0",
        "type": "function",
        "function": {
          "name": "web_search",
          "arguments": "{\"query\": \"天蓝色的 RGB 是什么？\" }"
        }
      }
    ]
  },
  { /* 你需要补充的信息 */
    "role": "tool",
    "tool_call_id": "web_search:0",  /* 注意这儿的 id 需要和前面的 tool_calls[].id 对齐 */
    "content": "----MOONSHOT ENCRYPTED BEGIN----+nf6...DSM=----MOONSHOT ENCRYPTED END----"
  }
]

接下来模型就可以做进一步的推理了。

注意要点：

    模型可能会返回超过一个 tool_calls，因此你必须对所有 tool_calls 都给出返回模型才会继续，否则会认为请求不合法而拒绝请求

    assistant 如果带 tool_calls，接下来必定是和 tool_calls 完全一致的几个 role=tool 的 message，并且 tool_call_id 要求和前面的 tool_calls.id 一一对齐。

        如果有多个 tool_calls 顺序不敏感

        我们模型输出的 tool_calls 的几个 id 一定是唯一的，后面 role=tool 时候 id 也必须对齐

        仅在当轮这个 tool_calls - response 的局部有唯一性要求，对整个 conversation 或者全局这个唯一性不敏感

以下是一个 python 示例，以 web_search 工具为例，展示了如何通过 Kimi API 调用 Formula 工具：

这里是您可以使用的 Kimi 官方 Formula 工具，您可以将 formula URI 增加到下方 demo 示例中体验：moonshot/convert:latest, moonshot/web-search:latest, moonshot/rethink:latest, moonshot/random-choice:latest, moonshot/mew:latest, moonshot/memory:latest, moonshot/excel:latest, moonshot/date:latest, moonshot/base64:latest

# Formula Chat Client - OpenAI chat with formula tools
# Uses MOONSHOT_BASE_URL and MOONSHOT_API_KEY for OpenAI client
 
import os
import json
import asyncio
import argparse
import httpx
from openai import AsyncOpenAI
 
 
class FormulaChatClient:
    def __init__(self, moonshot_base_url: str, api_key: str):
        self.openai = AsyncOpenAI(base_url=moonshot_base_url, api_key=api_key)
        self.httpx = httpx.AsyncClient(
            base_url=moonshot_base_url,
            headers={"Authorization": f"Bearer {api_key}"},
            timeout=30.0,
        )
        self.model = "kimi-k2-0711-preview"
 
    async def get_tools(self, formula_uri: str):
        response = await self.httpx.get(f"/formulas/{formula_uri}/tools")
        return response.json().get("tools", [])
 
    async def call_tool(self, formula_uri: str, function: str, args: dict):
        response = await self.httpx.post(
            f"/formulas/{formula_uri}/fibers",
            json={"name": function, "arguments": json.dumps(args)},
        )
        fiber = response.json()
 
        if fiber.get("status", "") == "succeeded":
            return fiber["context"].get("output") or fiber["context"].get(
                "encrypted_output"
            )
 
        if "error" in fiber:
            return f"Error: {fiber['error']}"
        if "error" in fiber.get("context", {}):
            return f"Error: {fiber['context']['error']}"
        if "output" in fiber.get("context", {}):
            return f"Error: {fiber['context']['output']}"
        return "Error: Unknown error"
 
    async def handle_response(self, response, messages, all_tools, tool_to_uri):
        message = response.choices[0].message
        messages.append(message)
        if not message.tool_calls:
            print(f"\nAI Response: {message.content}")
            return
 
        print(f"\nAI decided to use {len(message.tool_calls)} tool(s):")
 
        for call in message.tool_calls:
            func_name = call.function.name
            args = json.loads(call.function.arguments)
 
            print(f"\nCalling tool: {func_name}")
            print(f"Arguments: {json.dumps(args, ensure_ascii=False, indent=2)}")
 
            uri = tool_to_uri.get(func_name)
            if not uri:
                raise ValueError(f"No URI found for tool {func_name}")
 
            result = await self.call_tool(uri, func_name, args)
            if len(result) > 100:
                print(f"Tool result: {result[:100]}...")  # limit the output length
            else:
                print(f"Tool result: {result}")
 
            messages.append(
                {"role": "tool", "tool_call_id": call.id, "content": result}
            )
 
        next_response = await self.openai.chat.completions.create(
            model=self.model, messages=messages, tools=all_tools
        )
        await self.handle_response(next_response, messages, all_tools, tool_to_uri)
 
    async def chat(self, question, messages, all_tools, tool_to_uri):
        messages.append({"role": "user", "content": question})
        response = await self.openai.chat.completions.create(
            model=self.model, messages=messages, tools=all_tools
        )
        await self.handle_response(response, messages, all_tools, tool_to_uri)
 
    async def close(self):
        await self.httpx.aclose()
 
 
def normalize_formula_uri(uri: str) -> str:
    """Normalize formula URI with default namespace and tag"""
    if "/" not in uri:
        uri = f"moonshot/{uri}"
    if ":" not in uri:
        uri = f"{uri}:latest"
    return uri
 
 
async def main():
    parser = argparse.ArgumentParser(description="Chat with formula tools")
    parser.add_argument(
        "--formula",
        action="append",
        default=["moonshot/web-search:latest"],
        help="Formula URIs",
    )
    parser.add_argument("--question", help="Question to ask")
 
    args = parser.parse_args()
 
    # Process and deduplicate formula URIs
    raw_formulas = args.formula or ["moonshot/web-search:latest"]
    normalized_formulas = [normalize_formula_uri(uri) for uri in raw_formulas]
    unique_formulas = list(
        dict.fromkeys(normalized_formulas)
    )  # Preserve order while deduping
 
    print(f"Initialized formulas: {unique_formulas}")
 
    moonshot_base_url = os.getenv("MOONSHOT_BASE_URL", "https://api.moonshot.cn/v1")
    api_key = os.getenv("MOONSHOT_API_KEY")
 
 
    if not api_key:
        print("MOONSHOT_API_KEY required")
        return
 
    client = FormulaChatClient(moonshot_base_url, api_key)
 
    # Load and validate tools
    print("\nLoading tools from all formulas...")
    all_tools = []
    function_names = set()
    tool_to_uri = {}  # inverted index to the tool name
 
    for uri in unique_formulas:
        tools = await client.get_tools(uri)
        print(f"\nTools from {uri}:")
 
        for tool in tools:
            func = tool.get("function", None)
            if not func:
                print(f"Skipping tool using type: {tool.get('type', 'unknown')}")
                continue
            func_name = func.get("name")
            assert func_name, f"Tool missing name: {tool}"
            assert (
                func_name not in tool_to_uri
            ), f"ERROR: Tool '{func_name}' conflicts between {tool_to_uri.get(func_name)} and {uri}"
 
            if func_name in function_names:
                print(
                    f"ERROR: Duplicate function name '{func_name}' found across formulas"
                )
                print(f"Function {func_name} already exists in another formula")
                await client.close()
                return
 
            function_names.add(func_name)
            all_tools.append(tool)
            tool_to_uri[func_name] = uri
            print(f"  - {func_name}: {func.get('description', 'N/A')}")
 
    print(f"\nTotal unique tools loaded: {len(all_tools)}")
    if not all_tools:
        print("Warning: No tools found in any formula")
        return
 
    try:
        messages = [
            {
                "role": "system",
                "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。",
            }
        ]
        if args.question:
            print(f"\nUser: {args.question}")
            await client.chat(args.question, messages, all_tools, tool_to_uri)
        else:
            print("Chat mode (type 'q' to quit)")
            while True:
                question = input("\nQ: ").strip()
                if question.lower() == "q":
                    break
                if question:
                    await client.chat(question, messages, all_tools, tool_to_uri)
 
    finally:
        await client.close()
 
 
if __name__ == "__main__":
    asyncio.run(main())
 

Last updated on 2025年8月7日
在 Playground 中配置 ModelScope MCP 服务器
Prompt 最佳实践

如何在 Kimi API 中使用 Formula 工具 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    编写清晰的说明
    在请求中包含更多细节，可以获得更相关的回答
    在请求中要求模型扮演一个角色，可以获得更准确的输出
    在请求中使用分隔符来明确指出输入的不同部分
    明确完成任务所需的步骤
    向模型提供输出示例
    指定期望模型输出的长度
    提供参考文本
    指导模型使用参考文本来回答问题
    拆分复杂的任务
    通过分类来识别用户查询相关的指令
    对于轮次较长的对话应用程序，总结或过滤之前的对话
    分块概括长文档，并递归构建完整摘要

文档
入门指南
Prompt 最佳实践
Prompt 最佳实践

    System Prompt最佳实践：system prompt（系统提示）指的是模型在生成文本或响应之前所接收的初始输入或指令，这个提示对于模型的运作至关重要

    为什么需要向模型输出清晰的说明？

    模型无法读懂你的想法，如果输出内容太长，可要求模型简短回复。如果输出内容太简单，可要求模型进行专家级写作。如果你不喜欢输出的格式，请向模型展示你希望看到的格式。模型越少猜测你的需求，你越有可能得到满意的结果。

    为了获得高度相关的输出，请保证在输入请求中提供所有重要细节和背景。

一般的请求	更好的请求
如何在Excel中增加数字？	我如何在Excel表对一行数字求和？我想自动为整张表的每一行进行求和，并将所有总计放在名为"总数"的最右列中。
工作汇报总结	将2023年工作记录总结为500字以内的段落。以序列形式列出每个月的工作亮点，并做出2023年全年工作总结。

    在 API 请求的'messages' 字段中增加指定模型在回复中使用的角色。

{
  "messages": [
    {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。"},
    {"role": "user", "content": "你好，我叫李雷，1+1等于多少？"}
  ]
}

    例如使用三重引号/XML标签/章节标题等定界符可以帮助区分需要不同处理的文本部分。

{
  "messages": [
    {"role": "system", "content": "你将收到两篇相同类别的文章，文章用XML标签分割。首先概括每篇文章的论点，然后指出哪篇文章提出了更好的论点，并解释原因。"},
    {"role": "user", "content": "<article>在这里插入文章</article><article>在这里插入文章</article>"}
  ]
}

{
  "messages": [
    {"role": "system", "content": "你将收到一篇论文的摘要和论文的题目。论文的题目应该让读者对论文主题有清晰的概念，同时也应该引人注目。如果你收到的标题不符合这些标准，请提出5个可选的替代方案"},
    {"role": "user", "content": "摘要:在这里插入摘要。\n\n标题:在这里插入标题"}
  ]
}

    任务建议明确一系列步骤。明确写出这些步骤可以使模型更容易遵循并获得更好的输出。

{
  "messages": [
    {"role": "system", "content": "使用以下步骤来回应用户输入。\n步骤一：用户将用三重引号提供文本。用前缀“摘要：”将这段文本概括成一句话。\n步骤二：将第一步的摘要翻译成英语，并加上前缀 "Translation: "。"},
    {"role": "user", "content": "\"\"\"在此处插入文本\"\"\""}
  ]
}

    向模型提供一般指导的示例描述，通常比展示任务的所有排列让模型的输出更加高效。例如，如果你打算让模型复制一种难以明确描述的风格，来回应用户查询。这被称为“few-shot”提示。

{
  "messages": [
    {"role": "system", "content": "以一致的风格回答"},
    {"role": "user", "content": "在此处插入文本"}
  ]
}

    你可以要求模型生成特定目标长度的输出。目标输出长度可以用文数、句子数、段落数、项目符号等来指定。但请注意，指示模型生成特定数量的文字并不具有高精度。模型更擅长生成特定数量的段落或项目符号的输出。

{
  "messages": [
    {"role": "user", "content": "用两句话概括三引号内的文本，50字以内。\"\"\"在此处插入文本\"\"\""}
  ]
}

    如果您可以提供一个包含与当前查询相关的可信信息的模型，那么就可以指导模型使用所提供的信息来回答问题

{
  "messages": [
    {"role": "system", "content": "使用提供的文章（用三引号分隔）回答问题。如果答案在文章中找不到，请写"我找不到答案。" "},
    {"role": "user", "content": "<请插入文章，每篇文章用三引号分隔>"}
  ]
}

    对于需要大量独立指令集来处理不同情况的任务来说，对查询类型进行分类，并使用该分类来明确需要哪些指令可能会帮助输出。

# 根据客户查询的分类，可以提供一组更具体的指示给模型，以便它处理后续步骤。例如，假设客户需要“故障排除”方面的帮助。
{
  "messages": [
    {"role": "system", "content": "你将收到需要技术支持的用户服务咨询。可以通过以下方式帮助用户：\n\n-请他们检查***是否配置完成。\n如果所有***都配置完成，但问题依然存在，请询问他们使用的设备型号\n-现在你需要告诉他们如何重启设备：\n=设备型号是A，请操作***。\n-如果设备型号是B，建议他们操作***。"}
  ]
}

    由于模型有固定的上下文长度显示，所以用户与模型助手之间的对话不能无限期地继续。

针对这个问题，一种解决方案是总结对话中的前几个回合。一旦输入的大小达到预定的阈值，就会触发一个查询来总结先前的对话部分，先前对话的摘要同样可以作为系统消息的一部分包含在内。或者，整个对话过程中的先前对话可以被异步总结。

    要总结一本书的内容，我们可以使用一系列的查询来总结文档的每个章节。部分摘要可以汇总并总结，产生摘要的摘要。这个过程可以递归进行，直到整本书都被总结完毕。如果需要使用前面的章节来理解后面的部分，那么可以在总结书中给定点的内容时，包括对给定点之前的章节的摘要。

Last updated on 2025年7月29日
在 Kimi API 中使用 Formula 工具
组织管理最佳实践

Prompt 最佳实践 - Moonshot AI 开放平台 - Kimi 大模型 API 服务 	
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    项目余额及限速说明
    项目消费管理
    项目个数限制
    组织成员管理
    项目成员管理
    项目 API Key 管理

文档
入门指南
组织管理最佳实践
建立并认证你的组织

您注册登陆到开放平台账号时，可以在【组织管理】-【组织认证】页面找到您的组织 ID，组织名称是您企业认证成功后的企业名称，组织 ID 是您组织的唯一标识。
管理项目及使用限额

为了满足单一组织下多业务产品线的使用，或者区分线上环境和测试环境的使用，您可以在组织下创建多个项目，在项目下创建 API Key，项目 API Key 的调用会记在项目消费中，方便您独立管理不同项目的消费。

    组织下的所有项目共享组织内的限速；
    组织下的所有项目共享组织的账户余额；

    平台已支持分项目设置月消费预算和日消费预算，您可以在【项目管理】-【项目设置】-【项目预算/限速设置】页面，设置项目的每月/日的消费额度，项目内的 API Key 消费使用达到预算后，任何后续该项目的 API 请求会被拒绝，可以有效帮助您管理业务预算。因计费周期的问题，实际执行限制预计会有 10 分钟左右的延迟。

    如果希望在项目使用量达到某特定金额时收到短信通知，您可以在【项目管理】-【项目设置】-【项目消费提醒】设置页面，设置每月/日消费提醒来管理您的 API 支出，平台计算自然月/日的消费达到限额后，会触发短信告警，发送短信到组织管理员的手机中。

    如果您希望对单个项目可使用的最大 TPM 做限制，可以独立配置项目的 TPM 限速值，项目 API Key 的请求达到该 TPM 后，请求会被拒绝。（项目 TPM 不得超过组织的 TPM ，若您设置超过组织 TPM 的数值，也会以组织 TPM 做限速）

    平台同时提供组织概览和项目概览页面，提供组织和项目纬度的消费分析，有助于您直观了解组织的消费情况。

组织可创建的项目个数依据组织认证类型而定，不同认证类型对应不同的项目创建数量上限，具体如下：
组织类型	项目个数上限	API Key 个数上限
未认证	1	10
个人认证	20	50
企业认证	50	100
企业级 API 用户	100	200

如有其他需求，请扫码联系 客服

咨询。
成员管理

为满足您管理组织的需求，您可以在【组织管理】-【成员管理】页面邀请新成员加入您的组织，平台会为该成员生成专属邀请链接，成员通过该链接注册登录开放平台后，可以加入组织。（仅企业认证组织可以邀请成员）

    企业管理员：组织账号的注册人默认为企业管理员。企业管理员可创建项目，邀请和管理成员，开具发票
    普通成员：普通成员仅可查看项目，普通成员需被邀请加入项目才会拥有项目资源的权限

组织管理员可创建项目，并邀请组织成员加入并管理项目，项目成员在项目中创建 API Key 以使用项目的资源。

    项目管理员：可管理设置项目预算/限速/消费通知/邀请成员/创建 API Key
    项目普通成员：仅可以查看项目/创建 API Key

建议每个项目成员在项目中创建自己的 API Key，不要共享。成员被移除项目后，成员创建的 API Key 也会同时失效，以便组织管理项目资源。
Last updated on 2025年7月30日
Prompt 最佳实践
常见问题及解决方案

建立并认证你的组织 - Moonshot AI 开放平台 - Kimi 大模型 API 服务
🚀 最新推出 kimi-k2-turbo-preview 高速版模型，限时促销中，快来体验吧！
Logo
Blog
文档
开发工作台
用户中心

    使用手册

    Chat
    Tool Use
    Partial Mode
    文件接口
    上下文缓存
    其它

🎉 促销活动

    模型推理定价
    上下文缓存定价
    工具定价
    充值与限速
    常见问题

    从 OpenAI 迁移到 Kimi API
    使用 API 调试工具
    开始使用 Kimi API
    使用 Kimi API 进行多轮对话
    使用 Vision 视觉模型
    自动选择 Kimi 模型
    自动断线重连
    使用 Stream 流式输出
    使用 Tool Calls
    使用联网搜索 Tool
    使用 JSON Mode
    使用 Partial Mode
    使用 Kimi API 进行文件问答
    使用 Context Caching
    使用 kimi-thinking-preview 长思考模型
    使用 Playground 调试模型
    在 software agents 中使用 kimi-k2 模型
    在 Playground 中配置 ModelScope MCP 服务器
    在 Kimi API 中使用 Formula 工具
    Prompt 最佳实践
    组织管理最佳实践
    常见问题及解决方案

        平台服务协议
        用户服务协议
        用户隐私协议
        充值协议

    Moonshot ↗

Changelog ↗
联系客服
开发者交流群
Global | platform.moonshot.ai↗

目录

    为什么 API 返回的结果和 Kimi 智能助手返回的结果不一致？
    Kimi API 是否拥有 Kimi 智能助手的“上网冲浪”功能
    Kimi API 返回的内容不完整或被截断
    报错 Your request exceeded model token limit，但输入内容非常短
    Kimi 大模型的输出长度是多少
    Kimi 大模型支持的汉字数量是多少？
    文件抽取内容不准确、图像无法被识别
    使用 files 接口时，希望使用 file_id 引用文件内容
    使用接口报错 content_filter: The request was rejected because it was considered high risk
    出现 Connection 相关错误
    报错信息显示的 TPM、RPM 限制与我的账户 Tier 等级不匹配
    报错 model_not_found
    Kimi 大模型出现数值计算错误
    Kimi 大模型无法回答今天的日期
    如何根据上下文长度选择恰当的模型
    在不使用 SDK 的场景下如何处理错误
    为何在提示词 prompt 相似的情况下，有的请求响应速度快，有的请求响应速度慢？
    我设置了 max_tokens=2000，让 Kimi 输出 2000 字的内容，但 Kimi 输出的内容少于 2000 字
    我在一分钟内只请求了一次，但却触发了 Your account reached max request 错误
    为了便于传输，我使用 base64 编码我的文本内容

文档
入门指南
常见问题及解决方案
常见问题及解决方案

API 和 Kimi 智能助手使用的是同一模型，如果你发现模型输出结果不一致，可以尝试修改 System Prompt；另一方面 Kimi 智能助手提供了诸如计算器等工具，而 API 并未默认提供这些工具，需要用户自行组装；

否。Kimi API 仅提供了大模型本身的交互功能，并不具备额外的“内容搜索”和“网页内容浏览”功能，也即是通常意义上的“联网搜索”功能。

现在，Kimi API 提供了联网搜索功能，请查阅我们的指南：

使用 Kimi API 的联网搜索功能

如果你想自己通过 Kimi API 实现联网搜索功能，也可以参考我们撰写的工具调用 tool_calls 指南：

使用 Kimi API 完成工具调用（tool_calls）

如果你想寻求开源社区的协助，你可以参考以下开源项目：

    search2ai

ArchiveBox

如果你想寻求由专业供应商提供的服务，有如下服务可供选择：

    apify

crawlbasejina reader

如果你发现 Kimi API 返回的内容不完整、被截断或长度不符合预期，你可以先检查响应体中的 choice.finish_reason 字段的值，如果该值为 length，则表明当前模型生成内容所包含的 Tokens 数量超过请求中的 max_tokens 参数，在这种情况下，Kimi API 仅会返回 max_tokens 个 Tokens 内容，多余的内容将会被丢弃，即上文所说“内容不完整”或“内容被截断”。

在遇到 finish_reason=length 时，如果你想让 Kimi 大模型接着上一次返回的内容继续输出，可以使用 Kimi API 提供的 Partial Mode，详细的文档请参考：

使用 Kimi API 的 Partial Mode

如果你想避免出现 finish_reason=length，我们建议你放大 max_tokens 的值，我们推荐的最佳实践是：通过 estimate-token-count

接口计算输入内容的 Tokens 数量，随后使用 Kimi 大模型所支持的最大 Tokens 数量（例如，对于 moonshot-v1-32k 模型，它最大支持 32k Tokens）减去输入内容的 Tokens 数量，得到的值即是本次请求的 max_tokens 值。

以 moonshot-v1-32k 模型举例：

max_tokens ＝ 32*1024 － prompt_tokens

我们会使用输入内容所占用的 Tokens 数量，加上请求设置的 max_tokens 值来判断当前请求是否超过 Kimi 大模型的上下文窗口大小，以 moonshot-v1-32k 为例，请确保：

prompt_tokens ＋ max_tokens ≤ 32*1024

    对于 moonshot-v1-8k 模型而言，最大输出长度是 8*1024 － prompt_tokens；
    对于 moonshot-v1-32k 模型而言，最大输出长度是 32*1024 － prompt_tokens；
    对于 moonshot-v1-128k 模型而言，最大输出长度是 128*1024 － prompt_tokens；

    对于 moonshot-v1-8k 模型而言，大约支持一万五千个汉字；
    对于 moonshot-v1-32k 模型而言，大约支持六万个汉字；
    对于 moonshot-v1-128k 模型而言，大约支持二十万个汉字；

注：以上均为估算值，实际情况可能有所不同。

我们提供各种格式的文件上传和文件解析服务，对于文本文件，我们会提取文件中的文字内容；对于图片文件，我们会使用 OCR 识别图片中的文字；对于 PDF 文档，如果 PDF 文档中只包含图片，我们会使用 OCR 提取图片中的文字，否则仅会提取文本内容。；

注意，对于图片，我们只会使用 OCR 提取图片中的文字内容，因此如果你的图片中不包含任何文字内容，则会引起解析失败的错误。

完整的文件格式支持列表，请参考：

文件接口

我们目前不支持使用文件 file_id 的方式引用文件内容作为上下文，但我们支持将文件内容缓存（使用 Context Caching 技术）后，使用 cache_id 或 cache_tag，引用已缓存的文件内容，实现类似的效果。

具体使用方式请参考：

使用 Kimi API 的 Context Caching 功能

当前请求 Kimi API 的输入或 Kimi 大模型的输出内容包含不安全或敏感内容，注意：Kimi 大模型生成的内容也可能包含不安全或敏感内容，进而导致 content_filter 错误。

如果在使用 Kimi API 的过程中，经常出现 Connection Error、Connection Time Out 等错误，请按照以下顺序检查：

    程序代码或使用的 SDK 是否有默认的超时设置；
    是否有使用任何类型的代理服务器，并检查代理服务器的网络和超时设置；
    是否从海外服务器访问 Kimi API，如果需要在海外请求 Kimi API，我们推荐将 base_url 替换成：

https://api-sg.moonshot.ai/v1

另一种可能导致 Connection 相关错误的场景是，未启用流式输出 stream=True 时，Kimi 大模型生成的 Tokens 数量过多，导致在等待 Kimi 大模型生成过程时，触发了某个中间环节网关的超时时间设置。通常，某些网关应用会通过检测是否接收到服务器端返回的 status_code 和 header 来判断当前请求是否有效，在不使用流式输出 stream=True 的场合，Kimi 服务端会等待 Kimi 大模型生成完毕后发送 header，在等待 header 返回时，某些网关应用会关闭等待时间过长的连接，进而产生 Connection 相关错误。

我们推荐启用流式输出 stream=True 来尽可能减少 Connection 相关错误。

如果你在使用 Kimi API 的过程遇到了 rate_limit_reached_error 错误，例如：

rate_limit_reached_error: Your account {uid}<{ak-id}> request reached TPM rate limit, current:{current_tpm}, limit:{max_tpm}

但报错信息中的 TPM 或 RPM 限制与你在后台查看的 TPM 与 RPM 并不匹配，请先排查是否正确使用了当前账户的 api_key；通常情况下 TPM、RPM 与预期不匹配的原因，是使用了错误的 api_key，例如误用了其他用户给予的 api_key，或个人拥有多个账号的情况下，混用了 api_key。

请确保你在 SDK 中正确设置了 base_url=https://api.moonshot.cn，通常情况下，model_not_found 错误产生的原因是，使用 OpenAI SDK 时，未设置 base_url 值，导致请求被发送至 OpenAI 服务器，OpenAI 返回了 model_not_found 错误。

由于 Kimi 大模型生成过程的不确定性，在数值计算方面，Kimi 大模型可能会出现不同程度的计算错误，我们推荐使用工具调用 tool_calls 为 Kimi 大模型提供计算器功能，关于工具调用 tool_calls，可以参考我们撰写的工具调用 tool_calls 指南：

使用 Kimi API 完成工具调用（tool_calls）

Kimi 大模型无法获取像当前日期这样时效性非常强的信息，但你可以在系统提示词 system prompt 中为 Kimi 大模型提供这样的信息，例如：

import os
from datetime import datetime
from openai import OpenAI
 
client = OpenAI(
    api_key=os.environ['MOONSHOT_API_KEY'],
    base_url="https://api.moonshot.cn/v1",
)
 
# 我们通过 datetime 库生成了当前日期，并将其添加到系统提示词 system prompt 中
system_prompt = f"""
你是 Kimi，今天的日期是 {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}
"""
 
completion = client.chat.completions.create(
    model="moonshot-v1-128k",
    messages=[
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": "今天的日期？"},
    ],
    temperature=0.3,
)
 
print(completion.choices[0].message.content)  # 输出：今天的日期是 2024 年 7 月 31 日。
 

现在，你可以选择使用 model=moonshot-v1-auto 来让 Kimi 自动选择一个适配当前上下文长度的模型，请查阅我们的指南：

选择合适的 Kimi 大模型

我们可以根据输入上下文的长度，加上预期的输出 Tokens 长度来选择合适的模型，以下是一个自动选择模型的例子：

import os
import httpx
from openai import OpenAI
 
client = OpenAI(
    api_key=os.environ['MOONSHOT_API_KEY'],
    base_url="https://api.moonshot.cn/v1",
)
 
 
def estimate_token_count(input_messages) -> int:
    """
    在这里实现你的 Tokens 计算逻辑，或是直接调用我们的 Tokens 计算接口计算 Tokens
 
    https://api.moonshot.cn/v1/tokenizers/estimate-token-count
    """
    header = {
        "Authorization": f"Bearer {os.environ['MOONSHOT_API_KEY']}",
    }
    data = {
        "model": "moonshot-v1-128k",
        "messages": input_messages,
    }
    r = httpx.post("https://api.moonshot.cn/v1/tokenizers/estimate-token-count", headers=header, json=data)
    r.raise_for_status()
    return r.json()["data"]["total_tokens"]
 
 
def select_model(input_messages, max_tokens=1024) -> str:
    """
    select_model 根据输入的上下文消息 input_messages，以及预期的 max_tokens 值，
    选择一个大小合适的模型。
 
    select_model 内部会调用 estimate_token_count 函数计算 input_messages 所占用
    的 tokens 数量，加上 max_tokens 的值作为 total_tokens，并根据 total_tokens
    所处的区间选择恰当的模型。
    """
    prompt_tokens = estimate_token_count(input_messages)
    total_tokens = prompt_tokens + max_tokens
    if total_tokens <= 8 * 1024:
        return "moonshot-v1-8k"
    elif total_tokens <= 32 * 1024:
        return "moonshot-v1-32k"
    elif total_tokens <= 128 * 1024:
        return "moonshot-v1-128k"
    else:
        raise Exception("too many tokens 😢")
 
 
messages = [
    {"role": "system", "content": "你是 Kimi"},
    {"role": "user", "content": "你好，请给我讲一个童话故事。"},
]
 
max_tokens = 2048
model = select_model(messages, max_tokens)
 
completion = client.chat.completions.create(
    model=model,
    messages=messages,
    max_tokens=max_tokens,
    temperature=0.3,
)
 
print("model:", model)
print("max_tokens:", max_tokens)
print("completion:", completion.choices[0].message.content)

在某些场合，你可能会需要自行对接 Kimi API（而不是使用 OpenAI SDK），在自行对接 Kimi API 时，你需要根据 API 返回的状态来决定后续的处理逻辑。通常而言，我们会使用 HTTP 状态码 200 表示请求成功，而使用 4xx、5xx 的状态码表示请求失败，我们会提供一个 JSON 格式的错误信息，关于请求状态具体的处理逻辑，请参考以下的代码片段：

import os
import httpx
 
header = {
    "Authorization": f"Bearer {os.environ['MOONSHOT_API_KEY']}",
}
 
messages = [
    {"role": "system", "content": "你是 Kimi"},
    {"role": "user", "content": "你好。"},
]
 
r = httpx.post("https://api.moonshot.cn/v1/chat/completions",
               headers=header,
               json={
                   "model": "moonshot-v1-128k",  # <-- 如果你使用一个正确的模型，下方会进入 if status_code==200 分支
                   # "model": "moonshot-v1-129k",  # <-- 如果你使用一个错误的模型名称，下方会进入 else 分支
                   "messages": messages,
                   "temperature": 0.3,
               })
 
if r.status_code == 200:  # 当使用正确的模型进行请求时，会进入此分支，进行正常的处理逻辑
    completion = r.json()
    print(completion["choices"][0]["message"]["content"])
else:  # 当使用错误的模型名称进行请求时，会进入此分支，在这里进行错误处理
    # 在这里，为了演示，我们仅将错误打印出来。
    # 在实际的代码逻辑中，你可能需要更多的处理逻辑，例如记录日志、中断请求或进行重试等。
    error = r.json()
    print(f"error: status={r.status_code}, type='{error['error']['type']}', message='{error['error']['message']}'")

我们的错误信息会遵循如下的格式：

{
	"error": {
		"type": "error_type",
		"message": "error_message"
	}
}

具体的错误信息对照表，请参考如下章节：

错误说明

如果你遇到在相似提示词 prompt 的不同请求中，有的请求响应快（例如响应时间只有 3s），有的请求响应慢（例如响应时间长达 20s），这通常是由于 Kimi 大模型生成的 Tokens 数量不同导致的。通常而言，Kimi 大模型生成的 Tokens 数量与 Kimi API 的响应时间成正比，生成的 Tokens 数量越多，API 完整的响应时间越长。

需要注意的是，Kimi 大模型生成的 Tokens 数量只影响完整请求（指生成完最后一个 Token）的响应时间，你可以设置 stream=True，并观察首 Token 返回时间（首 Token 返回时间，我们简称为 TTFT -- Time To First Token），通常情况下，提示词 prompt 的长度相似的场合，首 Token 响应时间不会有太大的波动。

max_tokens 参数的含义是：调用 /v1/chat/completions 时，允许模型生成的最大 Tokens 数量，当模型已经生成的 Tokens 数超过设置的 max_tokens 时，模型会停止输出下一个 Token。

max_tokens 的作用在于：

    帮助调用方确定该使用哪个模型（例如，当 prompt_tokens ＋ max_tokens ≤ 8 * 1024 时，可以选择 moonshot-v1-8k 模型）；
    防止在某些意外的场合，Kimi 模型输出了过多不符合预期的内容，进而导致额外的费用消耗（例如，Kimi 模型重复输出空白字符）；

max_tokens 并不能指示 Kimi 大模型输出多少 Tokens，换句话说，max_tokens 不会作为提示词 prompt 的一部分输入 Kimi 大模型，如果你想让模型输出特定字数的内容，可以参考以下通用的解决办法：

    对于要求输出内容字数在 1000 字以内的场合：
        在提示词 prompt 中向 Kimi 大模型明确输出的字数；
        通过人工或程序手段检测输出的字数是否符合预期，如果不符合预期，通过在第二轮对话中向 Kimi 大模型指示“字数多了”或“字数少了”，让 Kimi 大模型输出新一轮的内容。
    对于要求输出内容字数在 1000 字以上甚至更多时：
        尝试将预期输出的内容按结构或章节切割成若干部分，并制成模板，并使用占位符标记想要 Kimi 大模型输出内容的位置；
        让 Kimi 大模型按照模板，逐个填充每个模板的占位符部分，最终拼装成完整的长文文本。

通常，OpenAI 提供的 SDK 包含了重试机制：

    Certain errors are automatically retried 2 times by default, with a short exponential backoff. Connection errors (for example, due to a network connectivity problem), 408 Request Timeout, 409 Conflict, 429 Rate Limit, and >=500 Internal errors are all retried by default.

这种重试机制在遇到错误时，会默认重试 2 次（总计 3 次请求），通常来说，对于网络状况不稳定或者其他可能导致请求发生错误的场合，使用 OpenAI SDK 会将一个请求放大至 2 到 3 次请求，这些请求都会占用你的 RPM（每分钟请求数）次数。

注：对于使用 OpenAI SDK 且账户等级为 free 的用户而言，由于存在默认的重试机制，一次错误的请求就会消耗完所有的 RPM 额度。

请不要这样做，使用 base64 编码你的文件会导致产生巨量的 Tokens 消耗。如果你的文件类型是我们 /v1/files 文件接口支持的格式，使用文件接口上传并抽取文件内容即可。

对于二进制或其他格式编码的文件，Kimi 大模型暂时无法解析内容，请不要添加到上下文中。
Last updated on 2025年7月29日
组织管理最佳实践
平台服务协议

常见问题及解决方案 - Moonshot AI 开放平台 - Kimi 大模型 API 服务